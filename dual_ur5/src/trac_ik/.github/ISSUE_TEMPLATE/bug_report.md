---
name: Bug report
about: Create an issue highlighting a bug
title: ''
labels: bug
assignees: aprotyas

---

**Describe the bug**
A clear and concise description of what the bug is.

**Required Info:**
- Package:
  - <!-- Which of the packages in this repository is the bug in? -->
- Operating System:
  - <!-- OS and version (e.g. Windows 10, Ubuntu 16.04...) -->
- Installation type:
  - <!-- binaries or from source  -->
- Version or commit hash:
  - <!-- Output of git rev-parse HEAD, release version, or repos file  -->
- DDS implementation (if applicable):
  - <!-- rmw_implementation used (e.g. Fast-RTPS, RTI Connext, etc -->
- ROS 2 library (if applicable):
  - <!-- e.g. rclcpp, rclpy, or N/A -->

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Actual behavior**
A clear and concise description of what has actually been observed.

**Additional context**
Add any other context about the problem here.
