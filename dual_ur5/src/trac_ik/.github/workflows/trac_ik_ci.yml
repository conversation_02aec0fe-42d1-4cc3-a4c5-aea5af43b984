name: trac_ik CI

on:
  pull_request:
    paths-ignore: ['**.md']
  push:
    branches:
      - ros2
    paths-ignore: ['**.md']
  schedule:
    # Triggers the workflow at 00:00 UTC every day
    - cron: '0 0 * * *'

jobs:
  trac_ik-CI:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-20.04]
        ros_distribution: [foxy, galactic, rolling]
    steps:
      - uses: actions/checkout@v2.3.4

      - name: Setup ROS2
        uses: ros-tooling/setup-ros@v0.2
        with:
          use-ros2-testing: true
          required-ros-distributions: ${{ matrix.ros_distribution }}

      - name: Build and test trac_ik
        uses: ros-tooling/action-ros-ci@v0.2
        id: action_ros_ci
        with:
          package-name: |
            trac_ik
            trac_ik_examples
            trac_ik_lib
          target-ros2-distro: ${{ matrix.ros_distribution }}

      - name: Upload Logs
        uses: actions/upload-artifact@v2
        with:
          name: colcon_logs
          path: ${{ steps.action_ros_ci.outputs.ros-workspace-directory-name }}/log
        if: always()
