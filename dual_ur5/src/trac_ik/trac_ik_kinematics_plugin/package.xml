<?xml version="1.0"?>
<package format="3">
  <name>trac_ik_kinematics_plugin</name>
  <version>1.6.6</version>
  <description>A MoveIt! Kinematics plugin using TRAC-IK</description>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON></maintainer>
  <author><PERSON></author>
  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>moveit_core</build_depend>
  <build_depend>pluginlib</build_depend>
  <build_depend>roscpp</build_depend>
  <build_depend>tf_conversions</build_depend>
  <build_depend>trac_ik_lib</build_depend>
  <build_depend>libnlopt-dev</build_depend>
  <build_depend condition="$ROS_DISTRO == noetic">libnlopt-cxx-dev</build_depend>

  <exec_depend>moveit_core</exec_depend>
  <exec_depend>pluginlib</exec_depend>
  <exec_depend>roscpp</exec_depend>
  <exec_depend>tf_conversions</exec_depend>
  <exec_depend>trac_ik_lib</exec_depend>
  <exec_depend>libnlopt-dev</exec_depend>
  <exec_depend>libnlopt0</exec_depend>

  <export>
    <moveit_core plugin="${prefix}/trac_ik_kinematics_description.xml"/>
  </export>
</package>
