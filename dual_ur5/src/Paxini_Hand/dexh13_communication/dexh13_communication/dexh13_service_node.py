import rclpy
from rclpy.node import Node
import time
from dexh13_control_interface import DexH13Control, ControlMode  # 导入SDK接口及控制模式
# from dexh13_communication.dexh13_control_interface import DexH13Control, ControlMode
from my_interfaces.srv import SetJointAngles  # 自定义服务类型
from dexh13_communication.dexh13_control_interface import FingerAngle  # 引入 SDK 类

# 定义关节角度范围（弧度）
MIN_ANGLE = -0.349
MAX_ANGLE = 1.57

class DexH13ServiceNode(Node):
    def __init__(self):
        super().__init__('dexh13_service_node')
        # 创建服务，服务名为/set_joint_angles
        self.srv = self.create_service(SetJointAngles, 'set_joint_angles', self.set_joint_angles_callback)
        # 初始化SDK
        self.dexh13 = DexH13Control()
        self.connect_sdk()

    def connect_sdk(self):
        """连接灵巧手，遵循手册3.3.2操作流程"""
        self.get_logger().info("连接灵巧手SDK...")
        # 激活设备（端口根据实际情况修改，如/dev/ttyUSB0）
        self.dexh13.active_handy("/dev/ttyACM0", "none")
        time.sleep(6)  # 上电等待6s，否则指令可能失效
        if not self.dexh13.is_connect_handy():
            self.get_logger().error("灵巧手连接失败")
            return
        # 清除故障码并使能电机
        self.dexh13.clear_fault_code()
        self.dexh13.enable_motor()
        # 设置控制模式为位置控制模式
        self.set_control_mode(ControlMode.POSITION_CONTROL_MODE.value)
        self.get_logger().info("灵巧手连接成功，服务节点启动")

    def set_control_mode(self, control_mode):
        try:
            # 禁用电机
            en = self.dexh13.disable_motor()
            self.get_logger().info(f"disableMotor: {en}")

            # 检查电机是否禁用
            sig_enabled = self.dexh13.is_motor_enabled()
            self.get_logger().info(f"isMotorEnabled: {sig_enabled}")
            if sig_enabled:
                raise RuntimeError("disableMotor error")

            self.get_logger().info(f"set control mode: {control_mode}")
            self.dexh13.set_motor_control_mode(control_mode)
            # 设置控制模式后需要等待1 - 2秒，否则getMotorControlMode会不准确
            time.sleep(2)
            getMode = self.dexh13.get_motor_control_mode()
            # 获取控制模式
            self.get_logger().info(f"get Mode: {getMode}")
            if getMode != control_mode:
                # 如果控制模式不匹配，抛出异常
                raise RuntimeError("setControlMode error")

            en = self.dexh13.enable_motor()
            # 启用电机
            self.get_logger().info(f"enableMotor : {en}")

            sigEnabled = self.dexh13.is_motor_enabled()
            # 检查电机是否启用
            self.get_logger().info(f"isMotorEnabled: {sigEnabled}")
            if not sigEnabled:
                # 如果电机未启用，抛出异常
                raise RuntimeError("setEnable error")
        except Exception as e:
            self.get_logger().error(f"设置控制模式失败: {str(e)}")

    def set_joint_angles_callback(self, request, response):
        angles = request.joint_angles
        if len(angles) != 16:
            response.success = False
            response.message = "需要 16 个角度值"
            return response

        # 范围检查（SDK 期望角度制）
        for v in angles:
            if v < -20 or v > 90:
                response.success = False
                response.message = "角度需在 [-20, 90] 之间"
                return response

        # 每 4 个值 = 一根手指，共 4 根手指
        finger_angles = []
        for i in range(4):
            fa = FingerAngle()
            idx = i * 4
            fa.mcp1 = float(angles[idx])
            fa.mcp2 = float(angles[idx + 1])
            fa.pip  = float(angles[idx + 2])
            fa.dip  = float(angles[idx + 3])
            finger_angles.append(fa)

        # 调用 SDK 接口
        ok = self.dexh13.set_joint_positions_angle(finger_angles)
        self.get_logger().info(f"SDK 原始返回值: {ok}")
        response.success = bool(ok == 0)            # 0 → 成功
        response.message = "关节角度设置成功" if ok == 0 else f"SDK 错误码 {ok}"
        
        return response
       
  


def main(args=None):
    rclpy.init(args=args)
    node = DexH13ServiceNode()
    rclpy.spin(node)
    # 关闭节点时断开连接
    node.dexh13.disconnect_handy()
    node.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()

