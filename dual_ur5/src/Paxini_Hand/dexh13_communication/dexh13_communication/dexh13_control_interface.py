import sys

sys.path.append('../libs')

import os
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'lib')
os.environ['LD_LIBRARY_PATH'] = f"{lib_path}:{os.environ.get('LD_LIBRARY_PATH', '')}"

import libdexh13_control_python as dcp
import numpy as np
import enum


class HandType(enum.Enum):
    NONE_HAND = dcp.HandType.NONE_HAND
    LEFT_HAND = dcp.HandType.LEFT_HAND
    RIGHT_HAND = dcp.HandType.RIGHT_HAND


class ControlMode(enum.Enum):
    NONE_CONTROL_MODE = dcp.ControlMode.NONE_CONTROL_MODE
    TORQUE_CONTROL_MODE = dcp.ControlMode.TORQUE_CONTROL_MODE
    SPEED_CONTROL_MODE = dcp.ControlMode.SPEED_CONTROL_MODE
    POSITION_CONTROL_MODE = dcp.ControlMode.POSITION_CONTROL_MODE


class ErrorCode(enum.Enum):
    OVER_VOLT = dcp.ErrorCode.OVER_VOLT
    UNDER_VOLT = dcp.ErrorCode.UNDER_VOLT
    OVER_CURRENTU = dcp.ErrorCode.OVER_CURRENTU
    OVER_CURRENTV = dcp.ErrorCode.OVER_CURRENTV
    OVER_CURRENTW = dcp.ErrorCode.OVER_CURRENTW
    OVER_TEMPERATURE = dcp.ErrorCode.OVER_TEMPERATURE
    ADC_CALIB = dcp.ErrorCode.ADC_CALIB
    FOC_ERR = dcp.ErrorCode.FOC_ERR
    OVER_IQ = dcp.ErrorCode.OVER_IQ
    OPD_U = dcp.ErrorCode.OPD_U
    OPD_V = dcp.ErrorCode.OPD_V
    OPD_W = dcp.ErrorCode.OPD_W
    POS_REF_OVER = dcp.ErrorCode.POS_REF_OVER
    POS_REF_UNDER = dcp.ErrorCode.POS_REF_UNDER
    ABZ_ERR = dcp.ErrorCode.ABZ_ERR
    ABZ_Z_ERR = dcp.ErrorCode.ABZ_Z_ERR
    RESERVED0 = dcp.ErrorCode.RESERVED0
    RESERVED1 = dcp.ErrorCode.RESERVED1
    RESERVED2 = dcp.ErrorCode.RESERVED2
    RESERVED3 = dcp.ErrorCode.RESERVED3
    RESERVED4 = dcp.ErrorCode.RESERVED4
    RESERVED5 = dcp.ErrorCode.RESERVED5
    RESERVED6 = dcp.ErrorCode.RESERVED6
    RESERVED7 = dcp.ErrorCode.RESERVED7
    RESERVED8 = dcp.ErrorCode.RESERVED8
    RESERVED9 = dcp.ErrorCode.RESERVED9
    RESERVED10 = dcp.ErrorCode.RESERVED10
    RESERVED11 = dcp.ErrorCode.RESERVED11
    RESERVED12 = dcp.ErrorCode.RESERVED12
    RESERVED13 = dcp.ErrorCode.RESERVED13
    RESERVED14 = dcp.ErrorCode.RESERVED14
    DISCONNECT = dcp.ErrorCode.DISCONNECT


class FingerAngle:

    def __init__(self):
        self.mcp1 = 0.0
        self.mcp2 = 0.0
        self.pip = 0.0
        self.dip = 0.0

    def to_cpp(self):
        """
        将 Python FingerAngle 转换为 C++ FingerAngle 对象。
        """
        cpp_angle = dcp.FingerAngle()
        cpp_angle.mcp1 = self.mcp1
        cpp_angle.mcp2 = self.mcp2
        cpp_angle.pip = self.pip
        cpp_angle.dip = self.dip

        return cpp_angle


class Dex13MotorCurrent:

    def __init__(self):
        self.motor1_current = 0.0
        self.motor2_current = 0.0
        self.motor3_current = 0.0
        self.motor4_current = 0.0
        self.motor5_current = 0.0
        self.motor6_current = 0.0
        self.motor7_current = 0.0
        self.motor8_current = 0.0
        self.motor9_current = 0.0
        self.motor10_current = 0.0
        self.motor11_current = 0.0
        self.motor12_current = 0.0
        self.motor13_current = 0.0

    def to_cpp(self):
        cpp_current = dcp.Dex13MotorCurrent()
        cpp_current.motor1_current = self.motor1_current
        cpp_current.motor2_current = self.motor2_current
        cpp_current.motor3_current = self.motor3_current
        cpp_current.motor4_current = self.motor4_current
        cpp_current.motor5_current = self.motor5_current
        cpp_current.motor6_current = self.motor6_current
        cpp_current.motor7_current = self.motor7_current
        cpp_current.motor8_current = self.motor8_current
        cpp_current.motor9_current = self.motor9_current
        cpp_current.motor10_current = self.motor10_current
        cpp_current.motor11_current = self.motor11_current
        cpp_current.motor12_current = self.motor12_current
        cpp_current.motor13_current = self.motor13_current
        return cpp_current


class Dex13MotorSpeed:

    def __init__(self):
        self.motor1_speed = 0.0
        self.motor2_speed = 0.0
        self.motor3_speed = 0.0
        self.motor4_speed = 0.0
        self.motor5_speed = 0.0
        self.motor6_speed = 0.0
        self.motor7_speed = 0.0
        self.motor8_speed = 0.0
        self.motor9_speed = 0.0
        self.motor10_speed = 0.0
        self.motor11_speed = 0.0
        self.motor12_speed = 0.0
        self.motor13_speed = 0.0

    def to_cpp(self):
        cpp_speed = dcp.Dex13MotorSpeed()
        cpp_speed.motor1_speed = self.motor1_speed
        cpp_speed.motor2_speed = self.motor2_speed
        cpp_speed.motor3_speed = self.motor3_speed
        cpp_speed.motor4_speed = self.motor4_speed
        cpp_speed.motor5_speed = self.motor5_speed
        cpp_speed.motor6_speed = self.motor6_speed
        cpp_speed.motor7_speed = self.motor7_speed
        cpp_speed.motor8_speed = self.motor8_speed
        cpp_speed.motor9_speed = self.motor9_speed
        cpp_speed.motor10_speed = self.motor10_speed
        cpp_speed.motor11_speed = self.motor11_speed
        cpp_speed.motor12_speed = self.motor12_speed
        cpp_speed.motor13_speed = self.motor13_speed
        return cpp_speed


class FingerMotorsError:

    def __init__(self):
        self.finger_name = ""
        self.motor_ID = 0
        self.error_code = 0

    def to_cpp(self):
        cpp_error = dcp.FingerMotorsError()
        cpp_error.finger_name = self.finger_name
        cpp_error.motor_ID = self.motor_ID
        cpp_error.error_code = self.error_code
        return cpp_error


class FingerTemp:

    def __init__(self):
        self.finger_name = ""
        self.sensor_ID = 0
        self.temperature = 0.0

    def to_cpp(self):
        cpp_temp = dcp.FingerTemp()
        cpp_temp.finger_name = self.finger_name
        cpp_temp.sensor_ID = self.sensor_ID
        cpp_temp.temperature = self.temperature
        return cpp_temp


class DexFingerTac:

    def __init__(self):
        self.x = 0.0
        self.y = 0.0
        self.z = 0.0

    def to_cpp(self):
        cpp_finger_tac = dcp.DexFingerTac()
        cpp_finger_tac.x = self.x
        cpp_finger_tac.y = self.y
        cpp_finger_tac.z = self.z
        return cpp_finger_tac


class DexH13Control:

    def __init__(self):
        self.control = dcp.DexH13Control()

    def active_handy(self, handy_port_num, camera_port_num="none"):
        return self.control.activeHandy(handy_port_num, camera_port_num)

    def is_connect_handy(self):
        return self.control.isConnectHandy()

    def disconnect_handy(self):
        return self.control.disconnectHandy()

    def is_motor_enabled(self):
        return self.control.isMotorEnabled()

    def enable_motor(self):
        return self.control.enableMotor()

    def disable_motor(self):
        return self.control.disableMotor()

    def get_motor_control_mode(self):
        return self.control.getMotorControlMode()

    def is_fault(self):
        return self.control.isFault()

    def get_fault_code(self):
        return self.control.getFaultCode()

    def get_firmware_version(self):
        return self.control.getFirmwareVersion()

    def get_sdk_version(self):
        return self.control.getSDKVersion()

    def get_finger_tactile(self):
        return self.control.getFingerTactile()

    def get_finger_tactile_detail(self):
        return self.control.getFingerTactileDetail()

    def get_temperature(self):
        return self.control.getTemperature()

    def get_all_tactile_data(self):
        return self.control.getAllTactileData()

    def calibrate_sensor(self):
        return self.control.calibrateSensor()

    def clear_fault_code(self):
        return self.control.clearFaultCode()

    def set_motor_control_mode(self, mode):
        return self.control.setMotorControlMode(mode)

    def set_motor_max_current(self, max_current):
        return self.control.setMotorMaxCurrent(max_current)

    def set_motor_target_current(self, target_current):
        return self.control.setMotorTargetCurrent(target_current.to_cpp())

    def set_motor_max_speed(self, max_speed):
        return self.control.setMotorMaxSpeed(max_speed)

    def set_motor_target_speed(self, target_speed):
        return self.control.setMotorTargetSpeed(target_speed.to_cpp())

    def get_frame(self):
        frame = self.control.getFrame()
        return np.array(frame)

    def save_image(self, img_path, img):
        self.control.saveImage(img_path, img)

    def set_camera_config(self, width, height, fps):
        return self.control.setCameraConfig(width, height, fps)

    def get_intrinsic_matrix_and_dist_coeffs(self, width, height):
        intrinsic, dist_coeffs = self.control.getIntrinsicMatrixAndDistCoeffs(
            width, height)
        return np.array(intrinsic), np.array(dist_coeffs)

    def get_hand_eye_matrix(self):
        hand_eye_matrix = self.control.getHandEyeMatrix()
        return np.array(hand_eye_matrix)

    def undistort_image(self, input_img, camera_matrix, dist_coeffs):
        undistorted = self.control.undistortImage(input_img, camera_matrix,
                                                  dist_coeffs)
        return np.array(undistorted)

    def set_joint_positions_angle(self, finger_angle_list):
        finger_angle_list = [
            finger_angle.to_cpp() for finger_angle in finger_angle_list
        ]
        return self.control.setJointPositionsAngle(finger_angle_list)

    def get_joint_positions_angle(self):
        return self.control.getJointPositionsAngle()

    def set_joint_positions_radian(self, serial_pos):
        return self.control.setJointPositionsRadian(serial_pos)

    def get_joint_positions_radian(self):
        return self.control.getJointPositionsRadian()
