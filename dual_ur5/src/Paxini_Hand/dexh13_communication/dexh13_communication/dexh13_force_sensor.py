import sys,time,os

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(project_root, 'python'))

from  dexh13_control_interface import DexH13Control


def main():
    print("--- ----- --- DexTest Hand Start --- ----- --- ")

    # 创建 DexH13Control 对象
    dexh13 = DexH13Control()

    # 电机初始化使能
    try:
        # 电机初始化使能
        dexh13.active_handy("/dev/ttyACM0", "none")

        sigConnected = dexh13.is_connect_handy()
        print("--- ----- --- isConnected:", sigConnected)
        if not sigConnected:
            raise RuntimeError("isConnectHandy error")
    except RuntimeError as e:
        print(e)
        return
    
    # # 传感器校准，重启后需要重新校准
    dexh13.calibrate_sensor()
    time.sleep(1)


    # 设置查询频率
    hz = 10  # 1秒内查询的次数

    while True:
        # 查询 dexh13 各传感器x、y、z方向合力数据
        dexh13FingerTac = dexh13.get_finger_tactile()
        print("--- ----- --- dexh13 sensor data:")
        for tac in dexh13FingerTac:
            print("dexh13FingerTac x : ", tac.x, "y : ", tac.y, "z : ", tac.z)

        time.sleep(1 / hz)
        # 食指底部、食指中间、食指指尖、
        # 中指底部、中指中间、中指指尖、
        # 无名指底部、无名指中间、无名指指尖、
        # 拇指底部、拇指指尖

if __name__ == "__main__":
    main()