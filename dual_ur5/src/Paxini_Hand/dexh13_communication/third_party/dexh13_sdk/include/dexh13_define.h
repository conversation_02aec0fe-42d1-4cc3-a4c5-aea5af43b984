/**
 * @file: dexh13_define.h
 * @brief: This file contains the definitions and declarations for DexH13 control.
 * @version: 1.0.0
 */

#ifndef DEXH13_DEFINE_H
#define DEXH13_DEFINE_H

#include <iostream>
#include <memory>
#include <string>

namespace paxini::bot {

// 激活灵巧手的定义 none:0 left:1, right:2
enum HandType {
  NONE_HAND = 0,
  LEFT_HAND = 1,
  RIGHT_HAND = 2,
};

/**
 * 电机控制模式 DexH13 Motor Control Mode
 * 0: 自由模式
 * 1: 力矩控制模式
 * 2: 速度控制模式
 * 3: 位置控制模式
 */
enum ControlMode {
  NONE_CONTROL_MODE = 0,
  TORQUE_CONTROL_MODE = 1,
  SPEED_CONTROL_MODE = 2,
  POSITION_CONTROL_MODE = 3,
};

// 手指电机故障码 DexH13，枚举类 ErrorCode，用于表示不同的错误代码
enum class ErrorCode {
  OVER_VOLT = 0,
  UNDER_VOLT = 1,
  OVER_CURRENTU = 2,
  OVER_CURRENTV = 3,
  OVER_CURRENTW = 4,
  OVER_TEMPERATURE = 5,
  ADC_CALIB = 6,
  FOC_ERR = 7,
  OVER_IQ = 8,
  OPD_U = 9,
  OPD_V = 10,
  OPD_W = 11,
  POS_REF_OVER = 12,
  POS_REF_UNDER = 13,
  ABZ_ERR = 14,
  ABZ_Z_ERR = 15,
  RESERVED0 = 16,
  RESERVED1 = 17,
  RESERVED2 = 18,
  RESERVED3 = 19,
  RESERVED4 = 20,
  RESERVED5 = 21,
  RESERVED6 = 22,
  RESERVED7 = 23,
  RESERVED8 = 24,
  RESERVED9 = 25,
  RESERVED10 = 26,
  RESERVED11 = 27,
  RESERVED12 = 28,
  RESERVED13 = 29,
  RESERVED14 = 30,
  DISCONNECT = 31,
};

/* 速度 */
struct Dex13MotorSpeed {
  int16_t motor1_speed;
  int16_t motor2_speed;
  int16_t motor3_speed;
  int16_t motor4_speed;
  int16_t motor5_speed;
  int16_t motor6_speed;
  int16_t motor7_speed;
  int16_t motor8_speed;
  int16_t motor9_speed;
  int16_t motor10_speed;
  int16_t motor11_speed;
  int16_t motor12_speed;
  int16_t motor13_speed;
};

/* 电流 */
struct Dex13MotorCurrent {
  int16_t motor1_current;
  int16_t motor2_current;
  int16_t motor3_current;
  int16_t motor4_current;
  int16_t motor5_current;
  int16_t motor6_current;
  int16_t motor7_current;
  int16_t motor8_current;
  int16_t motor9_current;
  int16_t motor10_current;
  int16_t motor11_current;
  int16_t motor12_current;
  int16_t motor13_current;
};

// 手指电机错误码结构体：手指名称，电机ID，故障码对应关系的建立
// 手指名称有13：LeftThumb、LeftIndex、LeftMiddle、LeftRing、LeftPinky、RightThumb、RightIndex、RightMiddle、RightRing、RightPinky
// 电机有ID有：1、2、3、4、5、6、7、8、9、10、11、12、13
// 查看手指电机故障码 DexH13 Finger Motor Fault Code
struct FingerMotorsError {
  std::string finger_name;  // 手指名称
  int motor_ID;             // 手指电机ID
  ErrorCode error_code;     // 手指电机故障码
};

struct FingerTemp {
  std::string finger_name;  // 手指名称
  int sensor_ID;            // 传感器ID
  float temperature;        // 手指温度（手指上的传感器）
};

// 多维触觉传感器数据结构体
struct DexFingerTac {
  int x;
  int y;
  int z;
};

// FingerAngle 手指角度控制类型，模拟人体 Finger（食指 中指
// 环指，大拇指），每个 Finger 有三根指骨近节指骨、中节指骨和远节指骨，但是大拇指的结构与 Finger
// 有所不同，所谓的近节指骨在掌骨里，、所谓的中节指骨、远节指骨在掌骨外
struct FingerAngle {
  double mcp1;  // mcp1 近节摇摆角度
  double mcp2;  // mcp2 近节弯曲角度
  double pip;   // pip 中节弯曲角度
  double dip;   // dip 远节弯曲角度
};

class DexH13ControlImpl;  // forward declaration

}  // namespace paxini::bot
#endif  // DEXH13_DEFINE_H
