# from setuptools import setup
# import os
# from glob import glob

# package_name = 'dexh13_communication'

# # 获取 .so 动态库文件列表
# so_files = glob('third_party/dexh13_sdk/libs/*.so')

# setup(
#     name=package_name,
#     version='0.0.0',
#     packages=[package_name],  # 确保包含包名
#     data_files=[
#         ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
#         ('share/' + package_name, ['package.xml']),
#         (os.path.join('share', package_name, 'srv'), glob('srv/*.srv')),
#         (os.path.join('lib', 'python3.10', 'site-packages', package_name),
#          glob('dexh13_communication/*.py')),
#         (os.path.join('lib', 'python3.10', 'site-packages', package_name),
#          ['third_party/dexh13_sdk/python/dexh13_control_interface.py']),
#         (os.path.join('lib', package_name), so_files),
#     ],
#     install_requires=['setuptools'],
#     zip_safe=True,
#     maintainer='ccl-ubuntu',
#     maintainer_email='<EMAIL>',
#     description='DexH13 and data glove communication',
#     license='Apache-2.0',
#     entry_points={
#         'console_scripts': [
#             'dexh13_service_node = dexh13_communication.dexh13_service_node:main',
#             'glove_client_node = dexh13_communication.glove_client_node:main',
#         ],
#     },
# )


# from setuptools import setup
# import os
# from glob import glob
# from ament_python.setup_helpers import generate_distutils_setup

# package_name = 'dexh13_communication'

# # 获取 .so 动态库文件列表
# so_files = glob('third_party/dexh13_sdk/libs/*.so')

# # 生成 distutils 配置
# setup_args = generate_distutils_setup(
#     packages=[package_name],  # 确保包含包名
#     data_files=[
#         ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
#         ('share/' + package_name, ['package.xml']),
#         (os.path.join('share', package_name, 'srv'), glob('srv/*.srv')),
#         (os.path.join('lib', 'python3.10', 'site-packages', package_name),
#          glob('dexh13_communication/*.py')),
#         (os.path.join('lib', 'python3.10', 'site-packages', package_name),
#          ['third_party/dexh13_sdk/python/dexh13_control_interface.py']),
#         (os.path.join('lib', package_name), so_files),
#     ],
#     install_requires=['setuptools'],
#     zip_safe=True,
#     maintainer='ccl-ubuntu',
#     maintainer_email='<EMAIL>',
#     description='DexH13 and data glove communication',
#     license='Apache-2.0',
#     entry_points={
#         'console_scripts': [
#             'dexh13_service_node = dexh13_communication.dexh13_service_node:main',
#             'glove_client_node = dexh13_communication.glove_client_node:main',
#         ],
#     },
# )

# # 使用 setuptools 进行设置
# setup(
#     name=package_name,
#     version='0.0.0',
#     **setup_args
# )


# 修改后的 setup.py（移除 ament_python 依赖）
from setuptools import setup
import os
from glob import glob

package_name = 'dexh13_communication'

# 获取 .so 动态库文件列表
so_files = glob('third_party/dexh13_sdk/libs/*.so')

setup(
    name=package_name,
    version='0.0.0',
    packages=[package_name],  # 确保包含包名
    data_files=[
        ('share/ament_index/resource_index/packages', ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
        # (os.path.join('share', package_name, 'srv'), glob('srv/*.srv')),
        (os.path.join('lib', package_name),  # 安装到功能包的 lib 目录
        glob('third_party/dexh13_sdk/libs/*.so')),  # 所有 .so 文件
        # 安装 Python 模块到 ROS 2 识别的路径
        (os.path.join('lib', package_name), glob('dexh13_communication/*.py')),
        # (os.path.join('lib', 'python3.10', 'site-packages', package_name),
        # ['third_party/dexh13_sdk/python/dexh13_control_interface.py']),
        # (os.path.join('lib', package_name), ['third_party/dexh13_sdk/python/dexh13_control_interface.py']),
        # 安装动态库
        (os.path.join('lib', package_name), so_files),
        ('lib', glob('third_party/dexh13_sdk/libs/*.so')),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='ccl-ubuntu',
    maintainer_email='<EMAIL>',
    description='DexH13 and data glove communication',
    license='Apache-2.0',
    entry_points={
        'console_scripts': [
            'dexh13_service_node = dexh13_communication.dexh13_service_node:main',
            'glove_client_node = dexh13_communication.glove_client_node:main',
            'dexh13_topic_node = dexh13_communication.dexh13_topic_node:main',
            'force_sensor_node = dexh13_communication.dexh13_force_sensor:main',
        ],
    },
)
