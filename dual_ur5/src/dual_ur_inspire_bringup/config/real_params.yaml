controller_manager:
  ros__parameters:
    update_rate: 500

    joint_state_broadcaster:
      type: joint_state_broadcaster/JointStateBroadcaster

    ur_arm_ros2_controller:
      type:  position_controllers/JointGroupPositionController

ur_arm_ros2_controller:
  ros__parameters:
    joints:
      - left_shoulder_pan_joint
      - left_shoulder_lift_joint
      - left_elbow_joint
      - left_wrist_1_joint
      - left_wrist_2_joint
      - left_wrist_3_joint

    command_interfaces:
      - position

    state_interfaces:
      - position
      - velocity

arm_node:
  ros__parameters:
    debugging: false
    chain_root: world
    chain_tip: left_ee_link
    robot_arm_length: 0.917
    human_arm_length: 0.6
    z_init: 0.916
    Z_bias: 1.9
    X_bias: 0.0
    Y_bias: 0.25
    is_measure_ik_times: false
    interpolation_steps: 5
    origin_point_xyz: [0.5518, 0.10299, 0.24541]
    incremental_ratio: 1.15

vr_cam_node:
  ros__parameters:
    debugging: false
    not_use_mcp_abduction: true

/**:
  ros__parameters:
    use_sim_time: false
    use_rviz: true
    use_mock_sim: false
