from launch import LaunchDescription
from launch.actions import ExecuteProcess
import yaml
import os
from ament_index_python.packages import get_package_share_directory

def load_config_from_yaml(yaml_path):
    try:
        with open(yaml_path, 'r') as f:
            data = yaml.safe_load(f)
            return data if data is not None else {}
    except FileNotFoundError:
        print(f"Warning: Config file {yaml_path} not found, using defaults")
        return {}
    except yaml.YAMLError as e:
        print(f"Warning: Error parsing YAML file {yaml_path}: {e}")
        return {}

def generate_launch_description():
    bringup_pkg = get_package_share_directory('dual_ur_inspire_bringup')
    yaml_path = os.path.join(bringup_pkg, "config", "record_params.yaml")
    config = load_config_from_yaml(yaml_path)
    
    topics = config.get('topics', [])
    output_path = config.get('output_path', 'rosbag_recordings')
    
    if not topics:
        raise ValueError(f"No topics specified in {yaml_path}. Please configure topics to record.")

    # 构建ros2 bag record命令
    cmd = ['ros2', 'bag', 'record', '-o', output_path] + topics

    rosbag_record = ExecuteProcess(
        cmd=cmd,
        output='screen'
    )

    return LaunchDescription([
        rosbag_record,
    ])