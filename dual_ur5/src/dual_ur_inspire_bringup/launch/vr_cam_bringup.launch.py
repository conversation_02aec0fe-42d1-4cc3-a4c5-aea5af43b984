import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, OpaqueFunction
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    bringup_pkg = get_package_share_directory('dual_ur_inspire_bringup')
    declared_arguments = []

    declared_arguments.append(
        DeclareLaunchArgument(
            "params_config",
            default_value=os.path.join(bringup_pkg, "config", "real_params.yaml"),
            description="parameter configuration file",
        )
    )

    return LaunchDescription(
        declared_arguments + [OpaqueFunction(function=launch_setup)]
    )

def launch_setup(context, *args, **kwargs):
    params_config = context.launch_configurations['params_config']

    vr_cam_node = Node(
        package="vr_camera_node",
        executable='vr_cam_node',
        name='vr_cam_node',
        output="screen",
        parameters=[params_config],
    )
    # 启动云台控制节点（py_srvcli.gimbal_servo_topic:main）
    gimbal_servo_node = Node(
        package="py_srvcli",
        executable='gimbal_servo_ctrl',
        name='gimbal_servo_controller',
        output="screen",
        parameters=[{'port': '/dev/ttyUSB0', 'baudrate': 115200}],
    )

    nodes_to_start = [
        vr_cam_node,
        # gimbal_servo_node,
    ]

    return nodes_to_start