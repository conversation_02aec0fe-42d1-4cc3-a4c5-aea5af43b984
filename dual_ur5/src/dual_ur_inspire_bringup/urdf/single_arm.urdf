<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from single_arm.xacro               | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="dual_ur_robotiq">
  <link name="world"/>
  <joint name="fixed" type="fixed">
    <parent link="world"/>
    <child link="left_base_link"/>
    <origin rpy="0.0 0.0 -1.5707963267948966" xyz="0.0 0.0 0.0"/>
  </joint>
  <!-- Add URDF transmission elements (for ros_control) -->
  <!--<xacro:ur_arm_transmission prefix="${prefix}" hw_interface="${transmission_hw_interface}" />-->
  <!-- Placeholder for ros2_control transmission which don't yet exist -->
  <!-- links -  main serial chain -->
  <link name="left_base_link"/>
  <link name="left_base_link_inertia">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/visual/base.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/collision/base.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="4.0"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="0.00443333156" ixy="0.0" ixz="0.0" iyy="0.00443333156" iyz="0.0" izz="0.0072"/>
    </inertial>
  </link>
  <link name="left_shoulder_link">
    <visual>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/visual/shoulder.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/collision/shoulder.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="3.7"/>
      <origin rpy="0 0 0" xyz="0.0 -0.00193 -0.02561"/>
      <inertia ixx="0.014972358333333331" ixy="0" ixz="0" iyy="0.014972358333333331" iyz="0" izz="0.01040625"/>
    </inertial>
  </link>
  <link name="left_upper_arm_link">
    <visual>
      <origin rpy="1.5707963267948966 0 -1.5707963267948966" xyz="0 0 0.13585"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/visual/upperarm.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 -1.5707963267948966" xyz="0 0 0.13585"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/collision/upperarm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="8.393"/>
      <origin rpy="0 1.570796326794897 0" xyz="-0.2125 0.0 0.11336"/>
      <inertia ixx="0.13388583541666665" ixy="0" ixz="0" iyy="0.13388583541666665" iyz="0" izz="0.0151074"/>
    </inertial>
  </link>
  <link name="left_forearm_link">
    <visual>
      <origin rpy="1.5707963267948966 0 -1.5707963267948966" xyz="0 0 0.0165"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/visual/forearm.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 -1.5707963267948966" xyz="0 0 0.0165"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/collision/forearm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="2.33"/>
      <origin rpy="0 1.570796326794897 0" xyz="-0.24225 0.0 0.0265"/>
      <inertia ixx="0.031216803515624995" ixy="0" ixz="0" iyy="0.031216803515624995" iyz="0" izz="0.004095"/>
    </inertial>
  </link>
  <link name="left_wrist_1_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 -0.093"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/visual/wrist1.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 -0.093"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/collision/wrist1.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <origin rpy="0 0 0" xyz="0.0 -0.01634 -0.0018"/>
      <inertia ixx="0.002013889583333333" ixy="0" ixz="0" iyy="0.002013889583333333" iyz="0" izz="0.0021942"/>
    </inertial>
  </link>
  <link name="left_wrist_2_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.095"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/visual/wrist2.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.095"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/collision/wrist2.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <origin rpy="0 0 0" xyz="0.0 0.01634 -0.0018"/>
      <inertia ixx="0.0018310395833333333" ixy="0" ixz="0" iyy="0.0018310395833333333" iyz="0" izz="0.0021942"/>
    </inertial>
  </link>
  <link name="left_wrist_3_link">
    <visual>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 -0.0818"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/visual/wrist3.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="1.5707963267948966 0 0" xyz="0 0 -0.0818"/>
      <geometry>
        <mesh filename="package://ur_description/meshes/ur5/collision/wrist3.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1879"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 -0.001159"/>
      <inertia ixx="8.062475833333332e-05" ixy="0" ixz="0" iyy="8.062475833333332e-05" iyz="0" izz="0.0001321171875"/>
    </inertial>
  </link>
  <!-- base_joint fixes base_link to the environment -->
  <!-- <joint name="${tf_prefix}base_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent}" />
      <child link="${tf_prefix}base_link" />
    </joint> -->
  <!-- joints - main serial chain -->
  <joint name="left_base_link-base_link_inertia" type="fixed">
    <parent link="left_base_link"/>
    <child link="left_base_link_inertia"/>
    <!-- 'base_link' is REP-103 aligned (so X+ forward), while the internal
           frames of the robot/controller have X+ pointing backwards.
           Use the joint between 'base_link' and 'base_link_inertia' (a dummy
           link/frame) to introduce the necessary rotation over Z (of pi rad).
      -->
    <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
  </joint>
  <joint name="left_shoulder_pan_joint" type="revolute">
    <parent link="left_base_link_inertia"/>
    <child link="left_shoulder_link"/>
    <origin rpy="0 0 0" xyz="0 0 0.089159"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="6.283185307179586"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="left_shoulder_lift_joint" type="revolute">
    <parent link="left_shoulder_link"/>
    <child link="left_upper_arm_link"/>
    <origin rpy="1.570796327 0 0" xyz="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="6.283185307179586"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="left_elbow_joint" type="revolute">
    <parent link="left_upper_arm_link"/>
    <child link="left_forearm_link"/>
    <origin rpy="0 0 0" xyz="-0.425 0 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="6.283185307179586"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="left_wrist_1_joint" type="revolute">
    <parent link="left_forearm_link"/>
    <child link="left_wrist_1_link"/>
    <origin rpy="0 0 0" xyz="-0.39225 0 0.10915"/>
    <axis xyz="0 0 1"/>
    <limit effort="28.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="6.283185307179586"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="left_wrist_2_joint" type="revolute">
    <parent link="left_wrist_1_link"/>
    <child link="left_wrist_2_link"/>
    <origin rpy="1.570796327 0 0" xyz="0 -0.09465 -1.941303950897609e-11"/>
    <axis xyz="0 0 1"/>
    <limit effort="28.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="6.283185307179586"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <joint name="left_wrist_3_joint" type="revolute">
    <parent link="left_wrist_2_link"/>
    <child link="left_wrist_3_link"/>
    <origin rpy="1.570796326589793 3.141592653589793 3.141592653589793" xyz="0 0.0823 -1.688001216681175e-11"/>
    <axis xyz="0 0 1"/>
    <limit effort="28.0" lower="-6.283185307179586" upper="6.283185307179586" velocity="6.283185307179586"/>
    <dynamics damping="0" friction="0"/>
  </joint>
  <link name="left_ft_frame"/>
  <joint name="left_wrist_3_link-ft_frame" type="fixed">
    <parent link="left_wrist_3_link"/>
    <child link="left_ft_frame"/>
    <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
  </joint>
  <!-- ROS-Industrial 'base' frame - base_link to UR 'Base' Coordinates transform -->
  <link name="left_base"/>
  <joint name="left_base_link-base_fixed_joint" type="fixed">
    <!-- Note the rotation over Z of pi radians - as base_link is REP-103
           aligned (i.e., has X+ forward, Y+ left and Z+ up), this is needed
           to correctly align 'base' with the 'Base' coordinate system of
           the UR controller.
      -->
    <origin rpy="0 0 3.141592653589793" xyz="0 0 0"/>
    <parent link="left_base_link"/>
    <child link="left_base"/>
  </joint>
  <!-- ROS-Industrial 'flange' frame - attachment point for EEF models -->
  <link name="left_flange"/>
  <joint name="left_wrist_3-flange" type="fixed">
    <parent link="left_wrist_3_link"/>
    <child link="left_flange"/>
    <origin rpy="0 -1.5707963267948966 -1.5707963267948966" xyz="0 0 0"/>
  </joint>
  <!-- ROS-Industrial 'tool0' frame - all-zeros tool frame -->
  <link name="left_tool0"/>
  <joint name="left_flange-tool0" type="fixed">
    <!-- default toolframe - X+ left, Y+ up, Z+ front -->
    <origin rpy="1.5707963267948966 0 1.5707963267948966" xyz="0 0 0"/>
    <parent link="left_flange"/>
    <child link="left_tool0"/>
  </joint>
  <ros2_control name="left_ur_arm_system" type="system">
    <hardware>
      <!-- <plugin>ur_robot_driver/URPositionHardwareInterface</plugin>
          <param name="robot_ip">${robot_ip}</param>
          <param name="script_filename">${script_filename}</param>
          <param name="output_recipe_filename">${output_recipe_filename}</param>
          <param name="input_recipe_filename">${input_recipe_filename}</param>
          <param name="headless_mode">${headless_mode}</param>
          <param name="reverse_port">${reverse_port}</param>
          <param name="script_sender_port">${script_sender_port}</param>
          <param name="reverse_ip">${reverse_ip}</param>
          <param name="script_command_port">${script_command_port}</param>
          <param name="trajectory_port">${trajectory_port}</param>
          <param name="tf_prefix">${prefix}</param>
          <param name="non_blocking_read">${non_blocking_read}</param>
          <param name="servoj_gain">2000</param>
          <param name="servoj_lookahead_time">0.03</param>
          <param name="use_tool_communication">${use_tool_communication}</param>
          <param name="kinematics/hash">${hash_kinematics}</param>
          <param name="tool_voltage">${tool_voltage}</param>
          <param name="tool_parity">${tool_parity}</param>
          <param name="tool_baud_rate">${tool_baud_rate}</param>
          <param name="tool_stop_bits">${tool_stop_bits}</param>
          <param name="tool_rx_idle_chars">${tool_rx_idle_chars}</param>
          <param name="tool_tx_idle_chars">${tool_tx_idle_chars}</param>
          <param name="tool_device_name">${tool_device_name}</param>
          <param name="tool_tcp_port">${tool_tcp_port}</param>
          <param name="keep_alive_count">${keep_alive_count}</param> -->
      <plugin>mock_components/GenericSystem</plugin>
      <param name="mock_sensor_commands">true</param>
      <param name="state_following_offset">0.0</param>
      <param name="calculate_dynamics">true</param>
      <param name="joints">
            [left_shoulder_pan_joint, left_shoulder_lift_joint, left_elbow_joint,
            left_wrist_1_joint, left_wrist_2_joint, left_wrist_3_joint]
          </param>
      <param name="command_interfaces">[position]</param>
      <param name="state_interfaces">[position, velocity]</param>
    </hardware>
    <!-- Each joint definition -->
    <joint name="left_shoulder_pan_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="left_shoulder_lift_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="left_elbow_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="left_wrist_1_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="left_wrist_2_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    <joint name="left_wrist_3_joint">
      <command_interface name="position"/>
      <command_interface name="velocity"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
  </ros2_control>
  <joint name="left_ee_fixed_joint" type="fixed">
    <parent link="left_wrist_3_link"/>
    <child link="left_ee_link"/>
    <origin rpy="0.0 -1.5707963267948966 1.5707963267948966" xyz="0.0 0.0 0.0"/>
  </joint>
  <link name="left_ee_link">
    <collision>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.01 0 0"/>
    </collision>
  </link>
</robot>
