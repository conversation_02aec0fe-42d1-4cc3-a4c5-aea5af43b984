# Visualisation
# UR16e uses the same parts as UR10e except the upper and lower arm are shortened

mesh_files:
  base:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/base.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/base.stl

  shoulder:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/shoulder.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/shoulder.stl

  upper_arm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur16e/visual/upperarm.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur16e/collision/upperarm.stl
      mesh_files:

  forearm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur16e/visual/forearm.dae
      material:
        name: "<PERSON><PERSON><PERSON>"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur16e/collision/forearm.stl

  wrist_1:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/wrist1.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/wrist1.stl
    visual_offset: -0.135

  wrist_2:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/wrist2.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/wrist2.stl
    visual_offset: -0.12

  wrist_3:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur10e/visual/wrist3.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur10e/collision/wrist3.stl
    visual_offset: -0.1168
