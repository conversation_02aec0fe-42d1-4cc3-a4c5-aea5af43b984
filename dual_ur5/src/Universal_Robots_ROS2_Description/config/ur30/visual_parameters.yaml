# Visualisation

mesh_files:
  base:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur20/visual/base.dae
      material:
        name: "<PERSON><PERSON><PERSON>"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur20/collision/base.stl

  shoulder:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur20/visual/shoulder.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur20/collision/shoulder.stl

  upper_arm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur30/visual/upperarm.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur30/collision/upperarm.stl
      mesh_files:

  forearm:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur30/visual/forearm.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur30/collision/forearm.stl

  wrist_1:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur20/visual/wrist1.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur20/collision/wrist1.stl
    visual_offset: -0.0775

  wrist_2:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur20/visual/wrist2.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur20/collision/wrist2.stl
    visual_offset: -0.0749

  wrist_3:
    visual:
      mesh:
        package: ur_description
        path: meshes/ur20/visual/wrist3.dae
      material:
        name: "LightGrey"
        color: "0.7 0.7 0.7 1.0"
    collision:
      mesh:
        package: ur_description
        path: meshes/ur20/collision/wrist3.stl
    visual_offset: -0.07
