---
# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
#
# See https://github.com/pre-commit/pre-commit


repos:
  # Standard hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
        args: ['--maxkb=1500']
      - id: check-ast
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-xml
      #- id: check-yaml
      - id: debug-statements
      - id: end-of-file-fixer
      - id: mixed-line-ending
      - id: check-byte-order-marker  # Forbid UTF-8 byte-order markers
      - id: trailing-whitespace
        # This need to be a one to one copy from the Universal Robots legal no matter trailing spaces or not
        exclude: |
          (?x)^(
            meshes/ur15/LICENSE.txt|
            meshes/ur20/LICENSE.txt|
            meshes/ur30/LICENSE.txt
          )$

  # Python hooks
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.20.0
    hooks:
      - id: pyupgrade
        args: [--py36-plus]

  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        args: ["--line-length=100"]


  - repo: https://github.com/pycqa/flake8
    rev: 7.3.0
    hooks:
      - id: flake8
        args: ["--ignore=E501"]

  # CPP hooks
  # The same options as in ament_cppcheck are used, but its not working...
  #- repo: https://github.com/pocc/pre-commit-hooks
    #rev: v1.1.1
    #hooks:
      #- id: cppcheck
        #args: ['--error-exitcode=1', '-f', '--inline-suppr', '-q', '-rp', '--suppress=internalAstError', '--suppress=unknownMacro', '--verbose']


  # Cmake hooks
  - repo: local
    hooks:
      - id: ament_lint_cmake
        name: ament_lint_cmake
        description: Check format of CMakeLists.txt files.
        stages: [pre-commit]
        entry: ament_lint_cmake
        language: system
        files: CMakeLists.txt$

  # Copyright
  - repo: local
    hooks:
      - id: ament_copyright
        name: ament_copyright
        description: Check if copyright notice is available in all files.
        stages: [pre-commit]
        entry: ament_copyright
        language: system

  # Docs - RestructuredText hooks
  - repo: https://github.com/PyCQA/doc8
    rev: v2.0.0
    hooks:
      - id: doc8
        args: ['--max-line-length=100', '--ignore=D001']

  - repo: https://github.com/pre-commit/pygrep-hooks
    rev: v1.10.0
    hooks:
      - id: rst-backticks
      - id: rst-directive-colons
      - id: rst-inline-touching-normal

  # Spellcheck in comments and docs
  # skipping of *.svg files is not working...
  - repo: https://github.com/codespell-project/codespell
    rev: v2.4.1
    hooks:
      - id: codespell
        args: ['--write-changes']
        exclude: \.(svg|pyc)$
