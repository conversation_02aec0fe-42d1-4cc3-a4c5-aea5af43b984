name: Prerelease
on:
  push:
    branches:
      - ros2
    tags:
      - '**'
jobs:
  prerelease:
    name: prerelease build
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        ROS_DISTRO: [humble]
        ROS_REPO: [main]
        PRERELEASE: [true]
    env:
      CCACHE_DIR: ${{ github.workspace }}/.ccache
      BASEDIR: ${{ github.workspace }}/.work
      CACHE_PREFIX: ${{ matrix.ROS_DISTRO }}-${{ matrix.ROS_REPO }}
    steps:
      - uses: actions/checkout@v2
      # The target directory cache doesn't include the source directory because
      # that comes from the checkout.  See "prepare target_ws for cache" task below
      - name: cache target_ws
        if: ${{ ! matrix.CCOV }}
        uses: pat-s/always-upload-cache@v2.1.5
        with:
          path: ${{ env.BASEDIR }}/target_ws
          key: target_ws-${{ env.CACHE_PREFIX }}-${{ hashFiles('**/CMakeLists.txt', '**/package.xml') }}-${{ github.run_id }}
          restore-keys: |
            target_ws-${{ env.CACHE_PREFIX }}-${{ hashFiles('**/CMakeLists.txt', '**/package.xml') }}
      - name: cache ccache
        uses: pat-s/always-upload-cache@v2.1.5
        with:
          path: ${{ env.CCACHE_DIR }}
          key: ccache-${{ env.CACHE_PREFIX }}-${{ github.sha }}-${{ github.run_id }}
          restore-keys: |
            ccache-${{ env.CACHE_PREFIX }}-${{ github.sha }}
            ccache-${{ env.CACHE_PREFIX }}
      - uses: 'ros-industrial/industrial_ci@master'
        env:
          ROS_DISTRO: ${{ matrix.ROS_DISTRO }}
          ROS_REPO: ${{ matrix.ROS_REPO }}
          PRERELEASE: ${{ matrix.PRERELEASE }}

      - name: prepare target_ws for cache
        if: ${{ always() && ! matrix.CCOV }}
        run: |
          du -sh ${{ env.BASEDIR }}/target_ws
          sudo find ${{ env.BASEDIR }}/target_ws -wholename '*/test_results/*' -delete
          sudo rm -rf ${{ env.BASEDIR }}/target_ws/src
          du -sh ${{ env.BASEDIR }}/target_ws
