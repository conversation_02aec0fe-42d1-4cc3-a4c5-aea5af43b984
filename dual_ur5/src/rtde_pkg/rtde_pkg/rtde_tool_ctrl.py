#!/usr/bin/env python3
import rclpy
from sensor_msgs.msg import JointState
from rclpy.node import Node
from std_msgs.msg import Float64MultiArray
from geometry_msgs.msg import PoseStamped
from std_msgs.msg import Bool
from rclpy.qos import qos_profile_sensor_data
from rtde_control import RTDEControlInterface
from rtde_receive import RTDEReceiveInterface
import time
import math
import numpy as np
from scipy.spatial.transform import Rotation as R

class LeftCtrlNode(Node):

    def __init__(self,name):
        super().__init__(name)
        self.get_logger().info("大家好，我是%s!" % name)
        
        self.command_sub = self.create_subscription(PoseStamped,'/trans_pose',self.command_callback,qos_profile_sensor_data)
        self.start_flag_sub = self.create_subscription(Bool,'/start_flag',self.start_flag_callback,qos_profile_sensor_data)
        # self.timer = self.create_timer(1.0/100,self.timer_callback)     
        self.rtde_c = RTDEControlInterface("************")
        self.rtde_r = RTDEReceiveInterface("************")
        
        # 设置TCP (Tool Center Point) 根据URDF定义
        # self.setup_tcp()
        
        self.init_cmd = [1.57,-1.30,1.706,-0.36,1.64,-0.785]
        self.cmd = self.init_cmd
        
        self.init_flag = False
        self.start_flag = False
        self.start_cnt = 0
        
        self.acceleration=0.5
        self.speed=0.5
        self.time=0.008
        self.lookahead_time=0.1
        self.gain=300

        self.smooth_acceleration = 0.01
        self.smooth_speed = 0.02
        self.smooth_time = 0.008
        self.smooth_lookahead_time = 0.1
        self.smooth_gain = 150
        
        self.frequency = 0.0
        self.last_time = self.get_clock().now()
        
        self.rotation_z = -np.pi / 2 
        self.translation = np.array([0.0, 0.0, 0.0])

    def setup_tcp(self):
        """设置TCP根据URDF定义"""
        # 根据URDF: origin xyz="0.0 0.0 0.0" rpy="${-pi/4.0} ${-pi/2.0} ${pi/2.0}"
        # collision box offset: xyz="-0.01 0 0"
        
        # TCP位置 (相对于wrist_3_link): [x, y, z, rx, ry, rz]
        # 位置: collision box偏移 (-0.01, 0, 0)
        # 姿态: rpy=(-pi/4, -pi/2, pi/2) 转换为轴角表示
        tcp_pose = [
            -0.01,  # x: collision box偏移
            0.0,    # y
            0.0,    # z
            -math.pi/4.0,  # rx: -pi/4
            -math.pi/2.0,  # ry: -pi/2  
            math.pi/2.0    # rz: pi/2
        ]
        
        try:
            self.rtde_c.setTcp(tcp_pose)
            self.get_logger().info(f"TCP设置成功: {tcp_pose}")
        except Exception as e:
            self.get_logger().error(f"TCP设置失败: {e}")

    def timer_callback(self):
        if self.init_flag == False:
                self.rtde_c.moveJ(self.cmd)
                self.init_flag = True
        else:
            if self.frequency > 30:
                if self.start_flag == True:
                    if self.start_cnt >= 300:
                        self.rtde_c.servoJ(self.cmd,
                                self.acceleration,
                                self.speed,
                                self.time,
                                self.lookahead_time,
                                self.gain
                            )
                    else:
                        self.rtde_c.servoJ(self.cmd,
                            self.smooth_acceleration,
                            self.smooth_speed,
                            self.smooth_time,
                            self.smooth_lookahead_time,
                            self.smooth_gain
                        )
                        self.start_cnt += 1
                else:
                    self.rtde_c.servoJ(self.init_cmd,
                            self.smooth_acceleration,
                            self.smooth_speed,
                            self.smooth_time,
                            self.smooth_lookahead_time,
                            self.smooth_gain
                        )
            else:
                current_pos = self.rtde_r.getActualQ()
                self.rtde_c.servoJ(current_pos,
                                self.smooth_acceleration,
                                self.smooth_speed,
                                self.smooth_time,
                                self.smooth_lookahead_time,
                                self.smooth_gain
                            )
        
    def command_callback(self, msg):
        pose_world = msg.pose
    
        
        pos_world = np.array([pose_world.position.x, pose_world.position.y, pose_world.position.z])
        
        pos_world = np.array([0.5513,0.1028,0.24472])
        
        pos_base_link = self.rotate_z(pos_world, self.rotation_z) + self.translation
        
        rot_world = R.from_quat([
            pose_world.orientation.x,
            pose_world.orientation.y,
            pose_world.orientation.z,
            pose_world.orientation.w
        ])
        
        rot_world = R.from_quat([
            0.69211,-0.013229,-0.031597,0.72098
        ])
        
        rot_base_link = rot_world * R.from_euler('z', self.rotation_z)
        qx, qy, qz, qw = rot_base_link.as_quat()
        rotvec = rot_base_link.as_rotvec()
        
        self.get_logger().info(
            "\n==== 接收到的 world 坐标系位姿 ====\n"
            f"位置: [x: {pos_base_link[0]:.4f}, y: {pos_base_link[1]:.4f}, z: {pos_base_link[2]:.4f}]\n"
            f"姿态: [x: {pose_world.orientation.x:.4f}, y: {pose_world.orientation.y:.4f}, "
            f"z: {pose_world.orientation.z:.4f}, w: {pose_world.orientation.w:.4f}]\n"
            f"时间戳: {msg.header.stamp.sec}s {msg.header.stamp.nanosec}ns\n"
            f"坐标系: {msg.header.frame_id}"
        )
        
        # target_pose_base_link = [
        #     pos_base_link[0], pos_base_link[1], pos_base_link[2],
        #     rotvec[0], rotvec[1], rotvec[2]
        # ]
        
        target_pose_base_link = [
            -0.095, 0.55, -0.147,
            1.895, 0.137, 2.374
        ]
        
        # target_pose_base_link = [
        #     pos_base_link[0], pos_base_link[1], pos_base_link[2],
        #     qx, qy, qz, qw
        # ]
        
        self.rtde_c.servoL(target_pose_base_link, 0.5, 0.1, 0.01, 0.1, 300)
        
    def rotate_z(self, point, angle):
        rotation_matrix = np.array([
            [np.cos(angle), -np.sin(angle), 0],
            [np.sin(angle), np.cos(angle), 0],
            [0, 0, 1]
        ])
        return rotation_matrix.dot(point)

    def start_flag_callback(self, msg):
        if msg is not None and hasattr(msg, 'data'):
            self.start_flag = msg.data
            
    def __del__(self):
        if hasattr(self, 'rtde'):
            self.rtde.servoStop()
            self.rtde.disconnect()


def main(args=None):
    rclpy.init(args=args)
    node = LeftCtrlNode("rtde_left_ctrl_node")
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()