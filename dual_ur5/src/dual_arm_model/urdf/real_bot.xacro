<?xml version="1.0"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro" name="dual_ur_robotiq">

  <xacro:property name="PI" value="3.1415926535897931"/>
  <xacro:arg name="limited" default="false"/>

  <link name="world"/>

  <!-- Stand -->
    <link
    name="stand">
    <inertial>
      <origin
        xyz="-1.16162413021925E-09 0.00077335177474426 0.422760886105843"
        rpy="0 0 0" />
      <mass
        value="16.5920771320864" />
      <inertia
        ixx="1.32838756334005"
        ixy="1.45565226525382E-10"
        ixz="1.02837044309379E-08"
        iyy="1.32486542917323"
        iyz="6.09201131774427E-05"
        izz="0.418093274026382" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://dual_arm_model/meshes/stand.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="0.43921568627451 0.43921568627451 0.43921568627451 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://dual_arm_model/meshes/stand.STL" />
      </geometry>
    </collision>
  </link>

  <joint name="fixed" type="fixed">
    <parent link="world"/>
    <child link="stand"/>
  </joint>


<!-- (1) UR5 -->
  <xacro:include filename="$(find dual_arm_model)/urdf/ur5e.urdf.xacro" />
  <xacro:include filename="$(find dual_arm_model)/urdf/ur5e.ros2_control.xacro" />

  <!-- left arm -->
  <xacro:ur5e_robot prefix="left_" joint_limited="true" reverse_arm="false"/>
  <xacro:ur_arm_ros2_control prefix="left_" command_topic="left_arm_cmd"/>

  <!-- right arm -->
  <xacro:ur5e_robot prefix="right_" joint_limited="true" reverse_arm="true"/>
  <xacro:ur_arm_ros2_control prefix="right_" command_topic="right_arm_cmd"/>


  <!-- <joint name="left_arm_joint" type="fixed">
    <parent link="stand" />
    <child link = "left_base_link" />
    <origin xyz="0 0.35 1.8" rpy="${PI/4*2} 0 ${PI}" />
  </joint>

  <joint name="right_arm_joint" type="fixed">
    <parent link="stand" />
    <child link = "right_base_link" />
    <origin xyz="0 -0.35 1.8" rpy="${PI/4*2} 0 0" />
  </joint> -->

  <joint
    name="left_arm_joint" type="fixed">
    <origin xyz="0 0.25 1.05" rpy="${-PI/2} 0 0" />
    <parent
      link="stand" />
    <child
      link="left_base_link" />
    <axis
      xyz="0 0 0" />
  </joint>

  <joint name="right_arm_joint" type="fixed">
    <origin xyz="0 -0.25 1.05" rpy="${PI/2} 0 0" />
    <parent
      link="stand" />
    <child
      link="right_base_link" />
    <axis
      xyz="0 0 0" />
  </joint>

  <!-- (2) Inspire Hands -->
  <xacro:include filename="$(find dual_arm_model)/urdf/inspire_hand_left.xacro" />
  <xacro:include filename="$(find dual_arm_model)/urdf/inspire_hand_right.xacro" />

  <!-- Instantiate left Inspire hand -->
  <xacro:inspire_hand_left prefix="left_hand_" parent_link="left_ee_link">
    <origin xyz="0 0 0" rpy="0 0 ${PI/2}" />
  </xacro:inspire_hand_left>


  <!-- Instantiate right Inspire hand -->
  <xacro:inspire_hand_right prefix="right_hand_" parent_link="right_ee_link">
    <origin xyz="0 0 0" rpy="0 0 ${PI/2}" />
  </xacro:inspire_hand_right>

</robot>