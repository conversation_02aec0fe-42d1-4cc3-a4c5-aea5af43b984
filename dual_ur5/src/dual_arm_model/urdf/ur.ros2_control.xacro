<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

  <xacro:macro name="ur_arm_ros2_control" params="
    use_fake_hardware:=false fake_sensor_commands:=false
    sim_gazebo:=false
    sim_ignition:=false
    headless_mode:=false
    initial_positions:=${dict(shoulder_pan_joint=0.0,shoulder_lift_joint=-1.57,elbow_joint=0.0,wrist_1_joint=-1.57,wrist_2_joint=0.0,wrist_3_joint=0.0)}
    use_tool_communication:=false
    script_filename output_recipe_filename
    input_recipe_filename prefix
    hash_kinematics robot_ip
    tool_voltage:=0 tool_parity:=0 tool_baud_rate:=115200 tool_stop_bits:=1
    tool_rx_idle_chars:=1.5 tool_tx_idle_chars:=3.5 tool_device_name:=/tmp/ttyUR tool_tcp_port:=54321
    reverse_port:=50001
    script_sender_port:=50002
    reverse_ip:=0.0.0.0
    script_command_port:=50004
    trajectory_port:=50003
    non_blocking_read:=true
    keep_alive_count:=2
    ">

    <ros2_control name="${prefix}ur_arm_system" type="system">
      <hardware>
          <!-- <plugin>ur_robot_driver/URPositionHardwareInterface</plugin>
          <param name="robot_ip">${robot_ip}</param>
          <param name="script_filename">${script_filename}</param>
          <param name="output_recipe_filename">${output_recipe_filename}</param>
          <param name="input_recipe_filename">${input_recipe_filename}</param>
          <param name="headless_mode">${headless_mode}</param>
          <param name="reverse_port">${reverse_port}</param>
          <param name="script_sender_port">${script_sender_port}</param>
          <param name="reverse_ip">${reverse_ip}</param>
          <param name="script_command_port">${script_command_port}</param>
          <param name="trajectory_port">${trajectory_port}</param>
          <param name="tf_prefix">${prefix}</param>
          <param name="non_blocking_read">${non_blocking_read}</param>
          <param name="servoj_gain">2000</param>
          <param name="servoj_lookahead_time">0.03</param>
          <param name="use_tool_communication">${use_tool_communication}</param>
          <param name="kinematics/hash">${hash_kinematics}</param>
          <param name="tool_voltage">${tool_voltage}</param>
          <param name="tool_parity">${tool_parity}</param>
          <param name="tool_baud_rate">${tool_baud_rate}</param>
          <param name="tool_stop_bits">${tool_stop_bits}</param>
          <param name="tool_rx_idle_chars">${tool_rx_idle_chars}</param>
          <param name="tool_tx_idle_chars">${tool_tx_idle_chars}</param>
          <param name="tool_device_name">${tool_device_name}</param>
          <param name="tool_tcp_port">${tool_tcp_port}</param>
          <param name="keep_alive_count">${keep_alive_count}</param> -->
          <plugin>mock_components/GenericSystem</plugin>
          <param name="mock_sensor_commands">true</param>
          <param name="state_following_offset">0.0</param>
          <param name="calculate_dynamics">true</param>
          <param name="joints">
            [${prefix}shoulder_pan_joint, ${prefix}shoulder_lift_joint, ${prefix}elbow_joint,
            ${prefix}wrist_1_joint, ${prefix}wrist_2_joint, ${prefix}wrist_3_joint]
          </param>
          <param name="command_interfaces">[position]</param>
          <param name="state_interfaces">[position, velocity]</param>
      </hardware>
      <!-- Each joint definition -->
      <joint name="${prefix}shoulder_pan_joint">
        <command_interface name="position"/>
        <command_interface name="velocity"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
        <state_interface name="effort"/>
      </joint>

      <joint name="${prefix}shoulder_lift_joint">
        <command_interface name="position"/>
        <command_interface name="velocity"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
        <state_interface name="effort"/>
      </joint>

      <joint name="${prefix}elbow_joint">
        <command_interface name="position"/>
        <command_interface name="velocity"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
        <state_interface name="effort"/>
      </joint>

      <joint name="${prefix}wrist_1_joint">
        <command_interface name="position"/>
        <command_interface name="velocity"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
        <state_interface name="effort"/>
      </joint>

      <joint name="${prefix}wrist_2_joint">
        <command_interface name="position"/>
        <command_interface name="velocity"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
        <state_interface name="effort"/>
      </joint>

      <joint name="${prefix}wrist_3_joint">
        <command_interface name="position"/>
        <command_interface name="velocity"/>
        <state_interface name="position"/>
        <state_interface name="velocity"/>
        <state_interface name="effort"/>
      </joint>
    </ros2_control>
  </xacro:macro>

</robot>
