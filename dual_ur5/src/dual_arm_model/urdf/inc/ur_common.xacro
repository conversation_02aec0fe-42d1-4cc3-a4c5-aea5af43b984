<?xml version="1.0"?>
<robot xmlns:xacro="http://wiki.ros.org/xacro">
  <!--
    NOTE the macros defined in this file are NOT part of the public API of this
         package. Users CANNOT rely on this file being available, or stored in
         this location. Nor can they rely on the existence of the two macros.
         The macros store the defined properties in the scope of the caller.
         However, users MUST NOT rely on these properties, their contents or their
         names.

    Author: <PERSON><PERSON><PERSON>val
    Contributor: <PERSON>
  -->
  <xacro:macro name="cylinder_inertial" params="radius length mass *origin">
    <inertial>
      <mass value="${mass}" />
      <xacro:insert_block name="origin" />
      <inertia ixx="${0.0833333 * mass * (3 * radius * radius + length * length)}" ixy="0.0" ixz="0.0"
        iyy="${0.0833333 * mass * (3 * radius * radius + length * length)}" iyz="0.0"
        izz="${0.5 * mass * radius * radius}" />
    </inertial>
  </xacro:macro>

  <xacro:macro name="get_visual_params" params="name:=^ type:=^" >
    <xacro:if value="${type in sec_mesh_files[name]}">
      <xacro:property name="visual_params" value="${sec_mesh_files[name][type]}" scope="parent"/>
    </xacro:if>
    <xacro:unless value="${type in sec_mesh_files[name]}">
      <xacro:property name="visual_params" value="" scope="parent"/>
    </xacro:unless>
  </xacro:macro>

  <!-- Simplification of getting meshes. Available types can be seen in the visual_parameters.yaml (At the time of writing: visual, collision) -->
  <xacro:macro name="get_mesh_path" params="name:=^ type:=^" >
    <xacro:get_visual_params />
    <xacro:if value="${force_abs_paths}">
      <xacro:property name="mesh" value="file://$(find ${visual_params['mesh']['package']})/${visual_params['mesh']['path']}" scope="parent"/>
    </xacro:if>
    <xacro:unless value="${force_abs_paths}">
      <xacro:property name="mesh" value="package://${visual_params['mesh']['package']}/${visual_params['mesh']['path']}" scope="parent"/>
    </xacro:unless>
  </xacro:macro>

  <xacro:macro name="get_mesh" params="name type" >
    <xacro:get_mesh_path/>
    <mesh filename="${mesh}"/>
  </xacro:macro>

  <xacro:macro name="read_model_data" params="joint_limits_parameters_file kinematics_parameters_file physical_parameters_file visual_parameters_file force_abs_paths">

    <xacro:property name="force_abs_paths" value="${force_abs_paths}" scope="parent"/>
    <!-- Read .yaml files from disk, load content into properties -->
    <xacro:property name="config_joint_limit_parameters" value="${xacro.load_yaml(joint_limits_parameters_file)}"/>
    <xacro:property name="config_kinematics_parameters" value="${xacro.load_yaml(kinematics_parameters_file)}"/>
    <xacro:property name="config_physical_parameters" value="${xacro.load_yaml(physical_parameters_file)}"/>
    <xacro:property name="config_visual_parameters" value="${xacro.load_yaml(visual_parameters_file)}"/>

    <!-- Extract subsections from yaml dictionaries -->
    <xacro:property name="sec_limits" value="${config_joint_limit_parameters['joint_limits']}"/>
    <xacro:property name="sec_offsets" value="${config_physical_parameters['offsets']}"/>
    <xacro:property name="sec_inertia_parameters" value="${config_physical_parameters['inertia_parameters']}" />
    <xacro:property name="sec_mesh_files" value="${config_visual_parameters['mesh_files']}" scope="parent"/>
    <xacro:property name="sec_kinematics" value="${config_kinematics_parameters['kinematics']}" />

    <!-- JOINTS LIMIT PARAMETERS -->
    <xacro:property name="shoulder_pan_lower_limit" value="${sec_limits['shoulder_pan_joint']['min_position']}" scope="parent"/>
    <xacro:property name="shoulder_pan_upper_limit" value="${sec_limits['shoulder_pan_joint']['max_position']}" scope="parent"/>
    <xacro:property name="shoulder_pan_velocity_limit" value="${sec_limits['shoulder_pan_joint']['max_velocity']}" scope="parent"/>
    <xacro:property name="shoulder_pan_effort_limit" value="${sec_limits['shoulder_pan_joint']['max_effort']}" scope="parent"/>
    <xacro:property name="shoulder_lift_lower_limit" value="${sec_limits['shoulder_lift_joint']['min_position']}" scope="parent"/>
    <xacro:property name="shoulder_lift_upper_limit" value="${sec_limits['shoulder_lift_joint']['max_position']}" scope="parent"/>
    <xacro:property name="shoulder_lift_velocity_limit" value="${sec_limits['shoulder_lift_joint']['max_velocity']}" scope="parent"/>
    <xacro:property name="shoulder_lift_effort_limit" value="${sec_limits['shoulder_lift_joint']['max_effort']}" scope="parent"/>
    <xacro:property name="elbow_joint_lower_limit" value="${sec_limits['elbow_joint']['min_position']}" scope="parent"/>
    <xacro:property name="elbow_joint_upper_limit" value="${sec_limits['elbow_joint']['max_position']}" scope="parent"/>
    <xacro:property name="elbow_joint_velocity_limit" value="${sec_limits['elbow_joint']['max_velocity']}" scope="parent"/>
    <xacro:property name="elbow_joint_effort_limit" value="${sec_limits['elbow_joint']['max_effort']}" scope="parent"/>
    <xacro:property name="wrist_1_lower_limit" value="${sec_limits['wrist_1_joint']['min_position']}" scope="parent"/>
    <xacro:property name="wrist_1_upper_limit" value="${sec_limits['wrist_1_joint']['max_position']}" scope="parent"/>
    <xacro:property name="wrist_1_velocity_limit" value="${sec_limits['wrist_1_joint']['max_velocity']}" scope="parent"/>
    <xacro:property name="wrist_1_effort_limit" value="${sec_limits['wrist_1_joint']['max_effort']}" scope="parent"/>
    <xacro:property name="wrist_2_lower_limit" value="${sec_limits['wrist_2_joint']['min_position']}" scope="parent"/>
    <xacro:property name="wrist_2_upper_limit" value="${sec_limits['wrist_2_joint']['max_position']}" scope="parent"/>
    <xacro:property name="wrist_2_velocity_limit" value="${sec_limits['wrist_2_joint']['max_velocity']}" scope="parent"/>
    <xacro:property name="wrist_2_effort_limit" value="${sec_limits['wrist_2_joint']['max_effort']}" scope="parent"/>
    <xacro:if value="${sec_limits['wrist_3_joint']['has_position_limits']}">
      <xacro:property name="wrist_3_joint_type" value="revolute" scope="parent"/>
      <xacro:property name="wrist_3_lower_limit" value="${sec_limits['wrist_3_joint']['min_position']}" scope="parent"/>
      <xacro:property name="wrist_3_upper_limit" value="${sec_limits['wrist_3_joint']['max_position']}" scope="parent"/>
    </xacro:if>
    <xacro:unless value="${sec_limits['wrist_3_joint']['has_position_limits']}">
      <xacro:property name="wrist_3_joint_type" value="continuous" scope="parent"/>
    </xacro:unless>
    <xacro:property name="wrist_3_velocity_limit" value="${sec_limits['wrist_3_joint']['max_velocity']}" scope="parent"/>
    <xacro:property name="wrist_3_effort_limit" value="${sec_limits['wrist_3_joint']['max_effort']}" scope="parent"/>

    <!-- kinematics -->
    <xacro:property name="shoulder_x" value="${sec_kinematics['shoulder']['x']}" scope="parent"/>
    <xacro:property name="shoulder_y" value="${sec_kinematics['shoulder']['y']}" scope="parent"/>
    <xacro:property name="shoulder_z" value="${sec_kinematics['shoulder']['z']}" scope="parent"/>
    <xacro:property name="shoulder_roll" value="${sec_kinematics['shoulder']['roll']}" scope="parent"/>
    <xacro:property name="shoulder_pitch" value="${sec_kinematics['shoulder']['pitch']}" scope="parent"/>
    <xacro:property name="shoulder_yaw" value="${sec_kinematics['shoulder']['yaw']}" scope="parent"/>

    <xacro:property name="upper_arm_x" value="${sec_kinematics['upper_arm']['x']}" scope="parent"/>
    <xacro:property name="upper_arm_y" value="${sec_kinematics['upper_arm']['y']}" scope="parent"/>
    <xacro:property name="upper_arm_z" value="${sec_kinematics['upper_arm']['z']}" scope="parent"/>
    <xacro:property name="upper_arm_roll" value="${sec_kinematics['upper_arm']['roll']}" scope="parent"/>
    <xacro:property name="upper_arm_pitch" value="${sec_kinematics['upper_arm']['pitch']}" scope="parent"/>
    <xacro:property name="upper_arm_yaw" value="${sec_kinematics['upper_arm']['yaw']}" scope="parent"/>

    <xacro:property name="forearm_x" value="${sec_kinematics['forearm']['x']}" scope="parent"/>
    <xacro:property name="forearm_y" value="${sec_kinematics['forearm']['y']}" scope="parent"/>
    <xacro:property name="forearm_z" value="${sec_kinematics['forearm']['z']}" scope="parent"/>
    <xacro:property name="forearm_roll" value="${sec_kinematics['forearm']['roll']}" scope="parent"/>
    <xacro:property name="forearm_pitch" value="${sec_kinematics['forearm']['pitch']}" scope="parent"/>
    <xacro:property name="forearm_yaw" value="${sec_kinematics['forearm']['yaw']}" scope="parent"/>

    <xacro:property name="wrist_1_x" value="${sec_kinematics['wrist_1']['x']}" scope="parent"/>
    <xacro:property name="wrist_1_y" value="${sec_kinematics['wrist_1']['y']}" scope="parent"/>
    <xacro:property name="wrist_1_z" value="${sec_kinematics['wrist_1']['z']}" scope="parent"/>
    <xacro:property name="wrist_1_roll" value="${sec_kinematics['wrist_1']['roll']}" scope="parent"/>
    <xacro:property name="wrist_1_pitch" value="${sec_kinematics['wrist_1']['pitch']}" scope="parent"/>
    <xacro:property name="wrist_1_yaw" value="${sec_kinematics['wrist_1']['yaw']}" scope="parent"/>

    <xacro:property name="wrist_2_x" value="${sec_kinematics['wrist_2']['x']}" scope="parent"/>
    <xacro:property name="wrist_2_y" value="${sec_kinematics['wrist_2']['y']}" scope="parent"/>
    <xacro:property name="wrist_2_z" value="${sec_kinematics['wrist_2']['z']}" scope="parent"/>
    <xacro:property name="wrist_2_roll" value="${sec_kinematics['wrist_2']['roll']}" scope="parent"/>
    <xacro:property name="wrist_2_pitch" value="${sec_kinematics['wrist_2']['pitch']}" scope="parent"/>
    <xacro:property name="wrist_2_yaw" value="${sec_kinematics['wrist_2']['yaw']}" scope="parent"/>

    <xacro:property name="wrist_3_x" value="${sec_kinematics['wrist_3']['x']}" scope="parent"/>
    <xacro:property name="wrist_3_y" value="${sec_kinematics['wrist_3']['y']}" scope="parent"/>
    <xacro:property name="wrist_3_z" value="${sec_kinematics['wrist_3']['z']}" scope="parent"/>
    <xacro:property name="wrist_3_roll" value="${sec_kinematics['wrist_3']['roll']}" scope="parent"/>
    <xacro:property name="wrist_3_pitch" value="${sec_kinematics['wrist_3']['pitch']}" scope="parent"/>
    <xacro:property name="wrist_3_yaw" value="${sec_kinematics['wrist_3']['yaw']}" scope="parent"/>

    <!-- OFFSETS -->
    <xacro:property name="shoulder_offset" value="${sec_offsets['shoulder_offset']}" scope="parent"/>
    <xacro:property name="elbow_offset" value="${sec_offsets['elbow_offset']}" scope="parent"/>

    <!-- INERTIA PARAMETERS -->
    <!-- mass -->
    <xacro:property name="base_mass" value="${sec_inertia_parameters['base_mass']}" scope="parent"/>
    <xacro:property name="shoulder_mass" value="${sec_inertia_parameters['shoulder_mass']}" scope="parent"/>
    <xacro:property name="upper_arm_mass" value="${sec_inertia_parameters['upper_arm_mass']}" scope="parent"/>
    <xacro:property name="forearm_mass" value="${sec_inertia_parameters['forearm_mass']}" scope="parent"/>
    <xacro:property name="wrist_1_mass" value="${sec_inertia_parameters['wrist_1_mass']}" scope="parent"/>
    <xacro:property name="wrist_2_mass" value="${sec_inertia_parameters['wrist_2_mass']}" scope="parent"/>
    <xacro:property name="wrist_3_mass" value="${sec_inertia_parameters['wrist_3_mass']}" scope="parent"/>
    <!-- link inertia parameter -->
    <xacro:property name="intertia_links" value="${sec_inertia_parameters['links']}" scope="parent"/>
    <xacro:property name="base_inertia_radius" value="${intertia_links['base']['radius']}" scope="parent"/>
    <xacro:property name="base_inertia_length" value="${intertia_links['base']['length']}" scope="parent"/>

    <!-- center of mass -->
    <xacro:property name="prop_shoulder_cog" value="${sec_inertia_parameters['center_of_mass']['shoulder_cog']}" scope="parent"/>
    <xacro:property name="prop_upper_arm_cog" value="${sec_inertia_parameters['center_of_mass']['upper_arm_cog']}" scope="parent"/>
    <xacro:property name="prop_forearm_cog" value="${sec_inertia_parameters['center_of_mass']['forearm_cog']}" scope="parent"/>
    <xacro:property name="prop_wrist_1_cog" value="${sec_inertia_parameters['center_of_mass']['wrist_1_cog']}" scope="parent"/>
    <xacro:property name="prop_wrist_2_cog" value="${sec_inertia_parameters['center_of_mass']['wrist_2_cog']}" scope="parent"/>
    <xacro:property name="prop_wrist_3_cog" value="${sec_inertia_parameters['center_of_mass']['wrist_3_cog']}" scope="parent"/>

    <xacro:property name="shoulder_cog" value="${prop_shoulder_cog['x']} ${prop_shoulder_cog['y']} ${prop_shoulder_cog['z']}" scope="parent"/>
    <xacro:property name="upper_arm_cog" value="${prop_upper_arm_cog['x']} ${prop_upper_arm_cog['y']} ${prop_upper_arm_cog['z']}" scope="parent"/>
    <xacro:property name="forearm_cog" value="${prop_forearm_cog['x']} ${prop_forearm_cog['y']} ${prop_forearm_cog['z']}" scope="parent"/>
    <xacro:property name="wrist_1_cog" value="${prop_wrist_1_cog['x']} ${prop_wrist_1_cog['y']} ${prop_wrist_1_cog['z']}" scope="parent"/>
    <xacro:property name="wrist_2_cog" value="${prop_wrist_2_cog['x']} ${prop_wrist_2_cog['y']} ${prop_wrist_2_cog['z']}" scope="parent"/>
    <xacro:property name="wrist_3_cog" value="${prop_wrist_3_cog['x']} ${prop_wrist_3_cog['y']} ${prop_wrist_3_cog['z']}" scope="parent"/>

    <!-- inertia rotation -->
    <xacro:property name="prop_shoulder_inertia_rotation" value="${sec_inertia_parameters['rotation']['shoulder']}" scope="parent"/>
    <xacro:property name="prop_upper_arm_inertia_rotation" value="${sec_inertia_parameters['rotation']['upper_arm']}" scope="parent"/>
    <xacro:property name="prop_forearm_inertia_rotation" value="${sec_inertia_parameters['rotation']['forearm']}" scope="parent"/>
    <xacro:property name="prop_wrist_1_inertia_rotation" value="${sec_inertia_parameters['rotation']['wrist_1']}" scope="parent"/>
    <xacro:property name="prop_wrist_2_inertia_rotation" value="${sec_inertia_parameters['rotation']['wrist_2']}" scope="parent"/>
    <xacro:property name="prop_wrist_3_inertia_rotation" value="${sec_inertia_parameters['rotation']['wrist_3']}" scope="parent"/>

    <xacro:property name="shoulder_inertia_rotation" value="${prop_shoulder_inertia_rotation['roll']} ${prop_shoulder_inertia_rotation['pitch']} ${prop_shoulder_inertia_rotation['yaw']}" scope="parent"/>
    <xacro:property name="upper_arm_inertia_rotation" value="${prop_upper_arm_inertia_rotation['roll']} ${prop_upper_arm_inertia_rotation['pitch']} ${prop_upper_arm_inertia_rotation['yaw']}" scope="parent"/>
    <xacro:property name="forearm_inertia_rotation" value="${prop_forearm_inertia_rotation['roll']} ${prop_forearm_inertia_rotation['pitch']} ${prop_forearm_inertia_rotation['yaw']}" scope="parent"/>
    <xacro:property name="wrist_1_inertia_rotation" value="${prop_wrist_1_inertia_rotation['roll']} ${prop_wrist_1_inertia_rotation['pitch']} ${prop_wrist_1_inertia_rotation['yaw']}" scope="parent"/>
    <xacro:property name="wrist_2_inertia_rotation" value="${prop_wrist_2_inertia_rotation['roll']} ${prop_wrist_2_inertia_rotation['pitch']} ${prop_wrist_2_inertia_rotation['yaw']}" scope="parent"/>
    <xacro:property name="wrist_3_inertia_rotation" value="${prop_wrist_3_inertia_rotation['roll']} ${prop_wrist_3_inertia_rotation['pitch']} ${prop_wrist_3_inertia_rotation['yaw']}" scope="parent"/>

    <!-- tensors -->
    <xacro:property name="prop_shoulder_inertia" value="${sec_inertia_parameters['tensor']['shoulder']}" scope="parent"/>
    <xacro:property name="prop_upper_arm_inertia" value="${sec_inertia_parameters['tensor']['upper_arm']}" scope="parent"/>
    <xacro:property name="prop_forearm_inertia" value="${sec_inertia_parameters['tensor']['forearm']}" scope="parent"/>
    <xacro:property name="prop_wrist_1_inertia" value="${sec_inertia_parameters['tensor']['wrist_1']}" scope="parent"/>
    <xacro:property name="prop_wrist_2_inertia" value="${sec_inertia_parameters['tensor']['wrist_2']}" scope="parent"/>
    <xacro:property name="prop_wrist_3_inertia" value="${sec_inertia_parameters['tensor']['wrist_3']}" scope="parent"/>

    <xacro:property name="shoulder_inertia_ixx" value="${prop_shoulder_inertia['ixx']}" scope="parent"/>
    <xacro:property name="shoulder_inertia_ixy" value="${prop_shoulder_inertia['ixy']}" scope="parent"/>
    <xacro:property name="shoulder_inertia_ixz" value="${prop_shoulder_inertia['ixz']}" scope="parent"/>
    <xacro:property name="shoulder_inertia_iyy" value="${prop_shoulder_inertia['iyy']}" scope="parent"/>
    <xacro:property name="shoulder_inertia_iyz" value="${prop_shoulder_inertia['iyz']}" scope="parent"/>
    <xacro:property name="shoulder_inertia_izz" value="${prop_shoulder_inertia['izz']}" scope="parent"/>
    <xacro:property name="upper_arm_inertia_ixx" value="${prop_upper_arm_inertia['ixx']}" scope="parent"/>
    <xacro:property name="upper_arm_inertia_ixy" value="${prop_upper_arm_inertia['ixy']}" scope="parent"/>
    <xacro:property name="upper_arm_inertia_ixz" value="${prop_upper_arm_inertia['ixz']}" scope="parent"/>
    <xacro:property name="upper_arm_inertia_iyy" value="${prop_upper_arm_inertia['iyy']}" scope="parent"/>
    <xacro:property name="upper_arm_inertia_iyz" value="${prop_upper_arm_inertia['iyz']}" scope="parent"/>
    <xacro:property name="upper_arm_inertia_izz" value="${prop_upper_arm_inertia['izz']}" scope="parent"/>
    <xacro:property name="forearm_inertia_ixx" value="${prop_forearm_inertia['ixx']}" scope="parent"/>
    <xacro:property name="forearm_inertia_ixy" value="${prop_forearm_inertia['ixy']}" scope="parent"/>
    <xacro:property name="forearm_inertia_ixz" value="${prop_forearm_inertia['ixz']}" scope="parent"/>
    <xacro:property name="forearm_inertia_iyy" value="${prop_forearm_inertia['iyy']}" scope="parent"/>
    <xacro:property name="forearm_inertia_iyz" value="${prop_forearm_inertia['iyz']}" scope="parent"/>
    <xacro:property name="forearm_inertia_izz" value="${prop_forearm_inertia['izz']}" scope="parent"/>
    <xacro:property name="wrist_1_inertia_ixx" value="${prop_wrist_1_inertia['ixx']}" scope="parent"/>
    <xacro:property name="wrist_1_inertia_ixy" value="${prop_wrist_1_inertia['ixy']}" scope="parent"/>
    <xacro:property name="wrist_1_inertia_ixz" value="${prop_wrist_1_inertia['ixz']}" scope="parent"/>
    <xacro:property name="wrist_1_inertia_iyy" value="${prop_wrist_1_inertia['iyy']}" scope="parent"/>
    <xacro:property name="wrist_1_inertia_iyz" value="${prop_wrist_1_inertia['iyz']}" scope="parent"/>
    <xacro:property name="wrist_1_inertia_izz" value="${prop_wrist_1_inertia['izz']}" scope="parent"/>
    <xacro:property name="wrist_2_inertia_ixx" value="${prop_wrist_2_inertia['ixx']}" scope="parent"/>
    <xacro:property name="wrist_2_inertia_ixy" value="${prop_wrist_2_inertia['ixy']}" scope="parent"/>
    <xacro:property name="wrist_2_inertia_ixz" value="${prop_wrist_2_inertia['ixz']}" scope="parent"/>
    <xacro:property name="wrist_2_inertia_iyy" value="${prop_wrist_2_inertia['iyy']}" scope="parent"/>
    <xacro:property name="wrist_2_inertia_iyz" value="${prop_wrist_2_inertia['iyz']}" scope="parent"/>
    <xacro:property name="wrist_2_inertia_izz" value="${prop_wrist_2_inertia['izz']}" scope="parent"/>
    <xacro:property name="wrist_3_inertia_ixx" value="${prop_wrist_3_inertia['ixx']}" scope="parent"/>
    <xacro:property name="wrist_3_inertia_ixy" value="${prop_wrist_3_inertia['ixy']}" scope="parent"/>
    <xacro:property name="wrist_3_inertia_ixz" value="${prop_wrist_3_inertia['ixz']}" scope="parent"/>
    <xacro:property name="wrist_3_inertia_iyy" value="${prop_wrist_3_inertia['iyy']}" scope="parent"/>
    <xacro:property name="wrist_3_inertia_iyz" value="${prop_wrist_3_inertia['iyz']}" scope="parent"/>
    <xacro:property name="wrist_3_inertia_izz" value="${prop_wrist_3_inertia['izz']}" scope="parent"/>

    <!-- cylinder radius -->
    <xacro:property name="shoulder_radius" value="${sec_inertia_parameters['shoulder_radius']}" scope="parent"/>
    <xacro:property name="upper_arm_radius" value="${sec_inertia_parameters['upper_arm_radius']}" scope="parent"/>
    <xacro:property name="elbow_radius" value="${sec_inertia_parameters['elbow_radius']}" scope="parent"/>
    <xacro:property name="forearm_radius" value="${sec_inertia_parameters['forearm_radius']}" scope="parent"/>
    <xacro:property name="wrist_radius" value="${sec_inertia_parameters['wrist_radius']}" scope="parent"/>

    <!--KINEMATICS HASH-->
    <xacro:property name="kinematics_hash" value="${sec_kinematics['hash']}" scope="parent"/>
  </xacro:macro>
</robot>
