<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from real_bot.xacro                 | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="dual_ur_robotiq">
  <link name="world"/>
  <!-- Stand -->
  <link name="stand">
    <inertial>
      <origin rpy="0 0 0" xyz="-1.16162413021925E-09 0.00077335177474426 0.422760886105843"/>
      <mass value="16.5920771320864"/>
      <inertia ixx="1.32838756334005" ixy="1.45565226525382E-10" ixz="1.02837044309379E-08" iyy="1.32486542917323" iyz="6.09201131774427E-05" izz="0.418093274026382"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/stand.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.43921568627451 0.43921568627451 0.43921568627451 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/stand.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="fixed" type="fixed">
    <parent link="world"/>
    <child link="stand"/>
  </joint>
  <!--property name="shoulder_height" value="0.089159" /-->
  <!--property name="shoulder_offset" value="0.13585" /-->
  <!-- shoulder_offset - elbow_offset + wrist_1_length = 0.10915 -->
  <!--property name="upper_arm_length" value="0.42500" /-->
  <!--property name="elbow_offset" value="0.1197" /-->
  <!-- CAD measured -->
  <!--property name="forearm_length" value="0.39225" /-->
  <!--property name="wrist_1_length" value="0.093" /-->
  <!-- CAD measured -->
  <!--property name="wrist_2_length" value="0.09465" /-->
  <!-- In CAD this distance is 0.930, but in the spec it is 0.09465 -->
  <!--property name="wrist_3_length" value="0.0823" /-->
  <!-- manually measured -->
  <link name="left_base_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/base.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/base.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="4.0"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <inertia ixx="0.00443333156" ixy="0.0" ixz="0.0" iyy="0.00443333156" iyz="0.0" izz="0.0072"/>
    </inertial>
  </link>
  <joint name="left_shoulder_pan_joint" type="revolute">
    <parent link="left_base_link"/>
    <child link="left_shoulder_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.163"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.14"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="left_shoulder_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/shoulder.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/shoulder.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="3.7"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <inertia ixx="0.010267495893" ixy="0.0" ixz="0.0" iyy="0.010267495893" iyz="0.0" izz="0.00666"/>
    </inertial>
  </link>
  <joint name="left_shoulder_lift_joint" type="revolute">
    <parent link="left_shoulder_link"/>
    <child link="left_upper_arm_link"/>
    <origin rpy="0.0 1.5707963267948966 0.0" xyz="0.0 0.138 0.0"/>
    <axis xyz="0 1 0"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.14"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="left_upper_arm_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/upperarm.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/upperarm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="8.393"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.2125"/>
      <inertia ixx="0.1338857818623325" ixy="0.0" ixz="0.0" iyy="0.1338857818623325" iyz="0.0" izz="0.0151074"/>
    </inertial>
  </link>
  <joint name="left_elbow_joint" type="revolute">
    <parent link="left_upper_arm_link"/>
    <child link="left_forearm_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 -0.131 0.425"/>
    <axis xyz="0 1 0"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.14"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="left_forearm_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/forearm.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/forearm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="2.275"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.196"/>
      <inertia ixx="0.031179620861480004" ixy="0.0" ixz="0.0" iyy="0.031179620861480004" iyz="0.0" izz="0.004095"/>
    </inertial>
  </link>
  <joint name="left_wrist_1_joint" type="revolute">
    <parent link="left_forearm_link"/>
    <child link="left_wrist_1_link"/>
    <origin rpy="0.0 1.5707963267948966 0.0" xyz="0.0 0.0 0.392"/>
    <axis xyz="0 1 0"/>
    <limit effort="28.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="left_wrist_1_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/wrist1.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/wrist1.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <origin rpy="0 0 0" xyz="0.0 0.127 0.0"/>
      <inertia ixx="0.0025598989760400002" ixy="0.0" ixz="0.0" iyy="0.0025598989760400002" iyz="0.0" izz="0.0021942"/>
    </inertial>
  </link>
  <joint name="left_wrist_2_joint" type="revolute">
    <parent link="left_wrist_1_link"/>
    <child link="left_wrist_2_link"/>
    <origin rpy="0.0 0.0 1.5707963267948966" xyz="0.0 0.127 0.0"/>
    <axis xyz="0 0 1"/>
    <limit effort="28.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="left_wrist_2_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/wrist2.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/wrist2.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.1"/>
      <inertia ixx="0.0025598989760400002" ixy="0.0" ixz="0.0" iyy="0.0025598989760400002" iyz="0.0" izz="0.0021942"/>
    </inertial>
  </link>
  <joint name="left_wrist_3_joint" type="revolute">
    <parent link="left_wrist_2_link"/>
    <child link="left_wrist_3_link"/>
    <origin rpy="0.0 -1.5707963267948966 0.0" xyz="0.0 0.0 0.1"/>
    <axis xyz="0 1 0"/>
    <limit effort="28.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="left_wrist_3_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/wrist3.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/wrist3.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1879"/>
      <origin rpy="1.5707963267948966 0 0" xyz="0.0 0.0771 0.0"/>
      <inertia ixx="9.890410052167731e-05" ixy="0.0" ixz="0.0" iyy="9.890410052167731e-05" iyz="0.0" izz="0.0001321171875"/>
    </inertial>
  </link>
  <joint name="left_ee_fixed_joint" type="fixed">
    <parent link="left_wrist_3_link"/>
    <child link="left_ee_link"/>
    <origin rpy="0.0 0.0 1.5707963267948966" xyz="0.0 0.1 0.0"/>
  </joint>
  <link name="left_ee_link">
    <collision>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.01 0 0"/>
    </collision>
  </link>
  <!-- ROS base_link to UR 'Base' Coordinates transform -->
  <link name="left_base"/>
  <joint name="left_base_link-base_fixed_joint" type="fixed">
    <!-- NOTE: this rotation is only needed as long as base_link itself is
                 not corrected wrt the real robot (ie: rotated over 180
                 degrees)
      -->
    <origin rpy="0 0 -3.141592653589793" xyz="0 0 0"/>
    <parent link="left_base_link"/>
    <child link="left_base"/>
  </joint>
  <!-- Frame coincident with all-zeros TCP on UR controller -->
  <link name="left_tool0"/>
  <joint name="left_wrist_3_link-tool0_fixed_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0 0" xyz="0 0.1 0"/>
    <parent link="left_wrist_3_link"/>
    <child link="left_tool0"/>
  </joint>
  <!--property name="shoulder_height" value="0.089159" /-->
  <!--property name="shoulder_offset" value="0.13585" /-->
  <!-- shoulder_offset - elbow_offset + wrist_1_length = 0.10915 -->
  <!--property name="upper_arm_length" value="0.42500" /-->
  <!--property name="elbow_offset" value="0.1197" /-->
  <!-- CAD measured -->
  <!--property name="forearm_length" value="0.39225" /-->
  <!--property name="wrist_1_length" value="0.093" /-->
  <!-- CAD measured -->
  <!--property name="wrist_2_length" value="0.09465" /-->
  <!-- In CAD this distance is 0.930, but in the spec it is 0.09465 -->
  <!--property name="wrist_3_length" value="0.0823" /-->
  <!-- manually measured -->
  <link name="right_base_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/base.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/base.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="4.0"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <inertia ixx="0.00443333156" ixy="0.0" ixz="0.0" iyy="0.00443333156" iyz="0.0" izz="0.0072"/>
    </inertial>
  </link>
  <joint name="right_shoulder_pan_joint" type="revolute">
    <parent link="right_base_link"/>
    <child link="right_shoulder_link"/>
    <origin rpy="0.0 0.0 3.141592653589793" xyz="0.0 0.0 0.163"/>
    <axis xyz="0 0 1"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.14"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="right_shoulder_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/shoulder.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/shoulder.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="3.7"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
      <inertia ixx="0.010267495893" ixy="0.0" ixz="0.0" iyy="0.010267495893" iyz="0.0" izz="0.00666"/>
    </inertial>
  </link>
  <joint name="right_shoulder_lift_joint" type="revolute">
    <parent link="right_shoulder_link"/>
    <child link="right_upper_arm_link"/>
    <origin rpy="3.141592653589793 4.71238898038469 3.141592653589793" xyz="0.0 0.138 0.0"/>
    <axis xyz="0 1 0"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.14"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="right_upper_arm_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/upperarm.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/upperarm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="8.393"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.2125"/>
      <inertia ixx="0.1338857818623325" ixy="0.0" ixz="0.0" iyy="0.1338857818623325" iyz="0.0" izz="0.0151074"/>
    </inertial>
  </link>
  <joint name="right_elbow_joint" type="revolute">
    <parent link="right_upper_arm_link"/>
    <child link="right_forearm_link"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0 -0.131 0.425"/>
    <axis xyz="0 1 0"/>
    <limit effort="150.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.14"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="right_forearm_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/forearm.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/forearm.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="2.275"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.196"/>
      <inertia ixx="0.031179620861480004" ixy="0.0" ixz="0.0" iyy="0.031179620861480004" iyz="0.0" izz="0.004095"/>
    </inertial>
  </link>
  <joint name="right_wrist_1_joint" type="revolute">
    <parent link="right_forearm_link"/>
    <child link="right_wrist_1_link"/>
    <origin rpy="3.141592653589793 4.71238898038469 3.141592653589793" xyz="0.0 0.0 0.392"/>
    <axis xyz="0 1 0"/>
    <limit effort="28.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="right_wrist_1_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/wrist1.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/wrist1.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <origin rpy="0 0 0" xyz="0.0 0.127 0.0"/>
      <inertia ixx="0.0025598989760400002" ixy="0.0" ixz="0.0" iyy="0.0025598989760400002" iyz="0.0" izz="0.0021942"/>
    </inertial>
  </link>
  <joint name="right_wrist_2_joint" type="revolute">
    <parent link="right_wrist_1_link"/>
    <child link="right_wrist_2_link"/>
    <origin rpy="0.0 0.0 -1.5707963267948966" xyz="0.0 0.127 0.0"/>
    <axis xyz="0 0 1"/>
    <limit effort="28.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="right_wrist_2_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/wrist2.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/wrist2.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.219"/>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.1"/>
      <inertia ixx="0.0025598989760400002" ixy="0.0" ixz="0.0" iyy="0.0025598989760400002" iyz="0.0" izz="0.0021942"/>
    </inertial>
  </link>
  <joint name="right_wrist_3_joint" type="revolute">
    <parent link="right_wrist_2_link"/>
    <child link="right_wrist_3_link"/>
    <origin rpy="0.0 -1.5707963267948966 0.0" xyz="0.0 0.0 0.1"/>
    <axis xyz="0 1 0"/>
    <limit effort="28.0" lower="-3.141592653589793" upper="3.141592653589793" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="right_wrist_3_link">
    <visual>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/visual/wrist3.dae"/>
      </geometry>
      <material name="LightGrey">
        <color rgba="0.7 0.7 0.7 1.0"/>
      </material>
    </visual>
    <collision>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/ur5e/collision/wrist3.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1879"/>
      <origin rpy="1.5707963267948966 0 0" xyz="0.0 0.0771 0.0"/>
      <inertia ixx="9.890410052167731e-05" ixy="0.0" ixz="0.0" iyy="9.890410052167731e-05" iyz="0.0" izz="0.0001321171875"/>
    </inertial>
  </link>
  <joint name="right_ee_fixed_joint" type="fixed">
    <parent link="right_wrist_3_link"/>
    <child link="right_ee_link"/>
    <origin rpy="0.0 0.0 1.5707963267948966" xyz="0.0 0.1 0.0"/>
  </joint>
  <link name="right_ee_link">
    <collision>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.01 0 0"/>
    </collision>
  </link>
  <!-- ROS base_link to UR 'Base' Coordinates transform -->
  <link name="right_base"/>
  <joint name="right_base_link-base_fixed_joint" type="fixed">
    <!-- NOTE: this rotation is only needed as long as base_link itself is
                 not corrected wrt the real robot (ie: rotated over 180
                 degrees)
      -->
    <origin rpy="0 0 -3.141592653589793" xyz="0 0 0"/>
    <parent link="right_base_link"/>
    <child link="right_base"/>
  </joint>
  <!-- Frame coincident with all-zeros TCP on UR controller -->
  <link name="right_tool0"/>
  <joint name="right_wrist_3_link-tool0_fixed_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0 0" xyz="0 0.1 0"/>
    <parent link="right_wrist_3_link"/>
    <child link="right_tool0"/>
  </joint>
  <!-- <joint name="left_arm_joint" type="fixed">
    <parent link="stand" />
    <child link = "left_base_link" />
    <origin xyz="0 0.35 1.8" rpy="${PI/4*2} 0 ${PI}" />
  </joint>

  <joint name="right_arm_joint" type="fixed">
    <parent link="stand" />
    <child link = "right_base_link" />
    <origin xyz="0 -0.35 1.8" rpy="${PI/4*2} 0 0" />
  </joint> -->
  <joint name="left_arm_joint" type="fixed">
    <origin rpy="-1.5707963267948966 0 0" xyz="0 0.25 1.05"/>
    <parent link="stand"/>
    <child link="left_base_link"/>
    <axis xyz="0 0 0"/>
  </joint>
  <joint name="right_arm_joint" type="fixed">
    <origin rpy="1.5707963267948966 0 0" xyz="0 -0.25 1.05"/>
    <parent link="stand"/>
    <child link="right_base_link"/>
    <axis xyz="0 0 0"/>
  </joint>
  <joint name="left_hand_fixed_hand_joint" type="fixed">
    <parent link="left_ee_link"/>
    <child link="left_hand_L_hand_base_link"/>
    <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
  </joint>
  <link name="left_hand_L_hand_base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.002551 -0.066047 -0.0019357"/>
      <mass value="0.14143"/>
      <inertia ixx="0.0001234" ixy="2.1995E-06" ixz="-1.7694E-06" iyy="8.3835E-05" iyz="1.5968E-06" izz="7.7231E-05"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/L_hand_base_link.STL"/>
      </geometry>
      <material name="left_hand_mat_grey">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/L_hand_base_link.STL"/>
      </geometry>
    </collision>
  </link>
  <link name="left_hand_L_thumb_proximal_base">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0048817 0.00038782 -0.00722"/>
      <mass value="0.0018869"/>
      <inertia ixx="5.5158E-08" ixy="-1.1803E-08" ixz="-4.6743E-09" iyy="8.2164E-08" iyz="-1.3521E-09" izz="6.7434E-08"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link11_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link11_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_thumb_proximal_yaw_joint" type="revolute">
    <origin rpy="1.5708 -1.5708 0" xyz="-0.01696 -0.0691 0.02045"/>
    <parent link="left_hand_L_hand_base_link"/>
    <child link="left_hand_L_thumb_proximal_base"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="-0.1" upper="1.3" velocity="0.5"/>
  </joint>
  <link name="left_hand_L_thumb_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.021936 -0.01279 -0.0080386"/>
      <mass value="0.0066101"/>
      <inertia ixx="1.5693E-06" ixy="7.8339E-07" ixz="8.5959E-10" iyy="1.7356E-06" iyz="1.0378E-09" izz="2.787E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link12_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link12_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_thumb_proximal_pitch_joint" type="revolute">
    <origin rpy="-1.5708 0 0.16939" xyz="0.0099867 0.0098242 -0.0089"/>
    <parent link="left_hand_L_thumb_proximal_base"/>
    <child link="left_hand_L_thumb_proximal"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0.0" upper="0.5" velocity="0.5"/>
  </joint>
  <link name="left_hand_L_thumb_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0095531 0.0016282 -0.0072002"/>
      <mass value="0.0037844"/>
      <inertia ixx="3.6981E-07" ixy="9.8603E-08" ixz="-2.8173E-12" iyy="3.2395E-07" iyz="-2.8028E-12" izz="4.6532E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link13_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link13_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_thumb_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.04407 -0.034553 -0.0008"/>
    <parent link="left_hand_L_thumb_proximal"/>
    <child link="left_hand_L_thumb_intermediate"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="0.8" velocity="0.5"/>
    <mimic joint="left_hand_L_thumb_proximal_pitch_joint" multiplier="1.6" offset="0"/>
  </joint>
  <link name="left_hand_L_thumb_distal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0092888 -0.004953 -0.0060033"/>
      <mass value="0.003344"/>
      <inertia ixx="1.3632E-07" ixy="5.6787E-08" ixz="-9.1939E-11" iyy="1.4052E-07" iyz="1.2145E-10" izz="2.0026E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link14_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link14_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_thumb_distal_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.020248 -0.010156 -0.0012"/>
    <parent link="left_hand_L_thumb_intermediate"/>
    <child link="left_hand_L_thumb_distal"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.2" velocity="0.5"/>
    <mimic joint="left_hand_L_thumb_proximal_pitch_joint" multiplier="2.4" offset="0"/>
  </joint>
  <link name="left_hand_L_thumb_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="left_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="left_hand_L_thumb_tip_joint" type="fixed">
    <parent link="left_hand_L_thumb_distal"/>
    <child link="left_hand_L_thumb_tip"/>
    <origin rpy="0 0 0" xyz="0.015 -0.013 -0.004"/>
  </joint>
  <!-- Index Finger -->
  <link name="left_hand_L_index_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0012971 -0.011934 -0.0059998"/>
      <mass value="0.0042405"/>
      <inertia ixx="6.6215E-07" ixy="1.8442E-08" ixz="1.3746E-12" iyy="2.1167E-07" iyz="-1.4773E-11" izz="6.9402E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link15_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link15_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_index_proximal_joint" type="revolute">
    <origin rpy="-0.034907 0 0" xyz="0.00028533 -0.13653 0.032268"/>
    <parent link="left_hand_L_hand_base_link"/>
    <child link="left_hand_L_index_proximal"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
  </joint>
  <link name="left_hand_L_index_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0021753 -0.019567 -0.005"/>
      <mass value="0.0045682"/>
      <inertia ixx="7.6284E-07" ixy="-8.063E-08" ixz="3.6797E-13" iyy="9.4308E-08" iyz="1.5743E-13" izz="7.8176E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link16_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link16_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_index_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0024229 -0.032041 -0.001"/>
    <parent link="left_hand_L_index_proximal"/>
    <child link="left_hand_L_index_intermediate"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
    <mimic joint="left_hand_L_index_proximal_joint" multiplier="1" offset="0"/>
  </joint>
  <link name="left_hand_L_index_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="left_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="left_hand_L_index_tip_joint" type="fixed">
    <parent link="left_hand_L_index_intermediate"/>
    <child link="left_hand_L_index_tip"/>
    <origin rpy="0 0 0" xyz="-0.005 -0.04 -0.004"/>
  </joint>
  <!-- Middle Finger -->
  <link name="left_hand_L_middle_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0012971 -0.011934 -0.0059999"/>
      <mass value="0.0042405"/>
      <inertia ixx="6.6215E-07" ixy="1.8442E-08" ixz="1.2299E-12" iyy="2.1167E-07" iyz="-1.4484E-11" izz="6.9402E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link17_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link17_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_middle_proximal_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.00028533 -0.1371 0.01295"/>
    <parent link="left_hand_L_hand_base_link"/>
    <child link="left_hand_L_middle_proximal"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
  </joint>
  <link name="left_hand_L_middle_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.001921 -0.020796 -0.0049999"/>
      <mass value="0.0050397"/>
      <inertia ixx="9.5823E-07" ixy="-1.1425E-07" ixz="-2.4186E-12" iyy="1.0646E-07" iyz="3.6974E-12" izz="9.8385E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link18_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link18_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_middle_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0024229 -0.032041 -0.001"/>
    <parent link="left_hand_L_middle_proximal"/>
    <child link="left_hand_L_middle_intermediate"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
    <mimic joint="left_hand_L_middle_proximal_joint" multiplier="1" offset="0"/>
  </joint>
  <link name="left_hand_L_middle_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="left_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="left_hand_L_middle_tip_joint" type="fixed">
    <parent link="left_hand_L_middle_intermediate"/>
    <child link="left_hand_L_middle_tip"/>
    <origin rpy="0 0 0" xyz="-0.005 -0.045 -0.004"/>
  </joint>
  <!-- Ring Finger -->
  <link name="left_hand_L_ring_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0012971 -0.011934 -0.0059999"/>
      <mass value="0.0042405"/>
      <inertia ixx="6.6215E-07" ixy="1.8442E-08" ixz="9.6052E-13" iyy="2.1167E-07" iyz="-1.4124E-11" izz="6.9402E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link19_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link19_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_ring_proximal_joint" type="revolute">
    <origin rpy="0.05236 0 0" xyz="0.00028533 -0.13691 -0.0062872"/>
    <parent link="left_hand_L_hand_base_link"/>
    <child link="left_hand_L_ring_proximal"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
  </joint>
  <link name="left_hand_L_ring_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0021753 -0.019567 -0.005"/>
      <mass value="0.0045682"/>
      <inertia ixx="7.6285E-07" ixy="-8.0631E-08" ixz="3.3472E-14" iyy="9.4308E-08" iyz="-4.4773E-13" izz="7.8176E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link20_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link20_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_ring_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0024229 -0.032041 -0.001"/>
    <parent link="left_hand_L_ring_proximal"/>
    <child link="left_hand_L_ring_intermediate"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
    <mimic joint="left_hand_L_ring_proximal_joint" multiplier="1" offset="0"/>
  </joint>
  <link name="left_hand_L_ring_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="left_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="left_hand_L_ring_tip_joint" type="fixed">
    <parent link="left_hand_L_ring_intermediate"/>
    <child link="left_hand_L_ring_tip"/>
    <origin rpy="0 0 0" xyz="-0.002 -0.04 -0.004"/>
  </joint>
  <!-- Pinky Finger -->
  <link name="left_hand_L_pinky_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0012971 -0.011934 -0.0059999"/>
      <mass value="0.0042405"/>
      <inertia ixx="6.6215E-07" ixy="1.8442E-08" ixz="1.0279E-12" iyy="2.1167E-07" iyz="-1.4277E-11" izz="6.9402E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link21_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link21_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_pinky_proximal_joint" type="revolute">
    <origin rpy="0.10472 0 0" xyz="0.00028533 -0.13571 -0.025488"/>
    <parent link="left_hand_L_hand_base_link"/>
    <child link="left_hand_L_pinky_proximal"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
  </joint>
  <link name="left_hand_L_pinky_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0024788 -0.016208 -0.0050001"/>
      <mass value="0.0036036"/>
      <inertia ixx="4.3923E-07" ixy="-4.1355E-08" ixz="1.2263E-12" iyy="7.0315E-08" iyz="3.1311E-12" izz="4.4881E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link22_L.STL"/>
      </geometry>
      <material name="left_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link22_L.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="left_hand_L_pinky_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0024229 -0.032041 -0.001"/>
    <parent link="left_hand_L_pinky_proximal"/>
    <child link="left_hand_L_pinky_intermediate"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
    <mimic joint="left_hand_L_pinky_proximal_joint" multiplier="1" offset="0"/>
  </joint>
  <link name="left_hand_L_pinky_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="left_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="left_hand_L_pinky_tip_joint" type="fixed">
    <parent link="left_hand_L_pinky_intermediate"/>
    <child link="left_hand_L_pinky_tip"/>
    <origin rpy="0 0 0" xyz="-0.002 -0.032 -0.004"/>
  </joint>
  <joint name="right_hand_fixed_hand_joint" type="fixed">
    <parent link="right_ee_link"/>
    <child link="right_hand_R_hand_base_link"/>
    <origin rpy="0 0 1.5707963267948966" xyz="0 0 0"/>
  </joint>
  <!-- Base link -->
  <link name="right_hand_R_hand_base_link">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0025264 -0.066047 0.0019598"/>
      <mass value="0.14143"/>
      <inertia ixx="0.00012281" ixy="2.1711E-06" ixz="1.7709E-06" iyy="8.3832E-05" iyz="-1.6551E-06" izz="7.6663E-05"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/R_hand_base_link.STL"/>
      </geometry>
      <material name="right_hand_mat_grey">
        <color rgba="0.1 0.1 0.1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/R_hand_base_link.STL"/>
      </geometry>
    </collision>
  </link>
  <!-- Thumb -->
  <link name="right_hand_R_thumb_proximal_base">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.0048064 0.0009382 -0.00757"/>
      <mass value="0.0018869"/>
      <inertia ixx="5.816E-08" ixy="1.4539E-08" ixz="4.491E-09" iyy="7.9161E-08" iyz="-1.8727E-09" izz="6.7433E-08"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link11_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link11_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_thumb_proximal_yaw_joint" type="revolute">
    <origin rpy="1.5708 -1.5708 0" xyz="-0.01696 -0.0691 -0.02045"/>
    <parent link="right_hand_R_hand_base_link"/>
    <child link="right_hand_R_thumb_proximal_base"/>
    <axis xyz="0 0 -1"/>
    <limit effort="1" lower="-0.1" upper="1.3" velocity="0.5"/>
  </joint>
  <link name="right_hand_R_thumb_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.021932 0.012785 -0.0080386"/>
      <mass value="0.0066075"/>
      <inertia ixx="1.5686E-06" ixy="-7.8296E-07" ixz="8.9143E-10" iyy="1.7353E-06" iyz="-1.0191E-09" izz="2.786E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link12_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link12_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_thumb_proximal_pitch_joint" type="revolute">
    <origin rpy="1.5708 0 2.8587" xyz="-0.0088099 0.010892 -0.00925"/>
    <parent link="right_hand_R_thumb_proximal_base"/>
    <child link="right_hand_R_thumb_proximal"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0.0" upper="0.5" velocity="0.5"/>
  </joint>
  <link name="right_hand_R_thumb_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0095544 -0.0016282 -0.0071997"/>
      <mass value="0.0037847"/>
      <inertia ixx="3.6981E-07" ixy="-9.8581E-08" ixz="-4.7469E-12" iyy="3.2394E-07" iyz="1.0939E-12" izz="4.6531E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link13_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link13_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_thumb_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.04407 0.034553 -0.0008"/>
    <parent link="right_hand_R_thumb_proximal"/>
    <child link="right_hand_R_thumb_intermediate"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="0.8" velocity="0.5"/>
    <mimic joint="right_hand_R_thumb_proximal_pitch_joint" multiplier="1.6" offset="0"/>
  </joint>
  <link name="right_hand_R_thumb_distal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0092888 0.0049529 -0.0060033"/>
      <mass value="0.0033441"/>
      <inertia ixx="1.3632E-07" ixy="-5.6788E-08" ixz="-9.2764E-11" iyy="1.4052E-07" iyz="-1.2283E-10" izz="2.0026E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link14_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link14_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_thumb_distal_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.020248 0.010156 -0.0012"/>
    <parent link="right_hand_R_thumb_intermediate"/>
    <child link="right_hand_R_thumb_distal"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.2" velocity="0.5"/>
    <mimic joint="right_hand_R_thumb_proximal_pitch_joint" multiplier="2.4" offset="0"/>
  </joint>
  <link name="right_hand_R_thumb_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="right_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="right_hand_R_thumb_tip_joint" type="fixed">
    <parent link="right_hand_R_thumb_distal"/>
    <child link="right_hand_R_thumb_tip"/>
    <origin rpy="0 0 0" xyz="0.015 0.013 -0.004"/>
  </joint>
  <!-- Index Finger -->
  <link name="right_hand_R_index_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0012259 0.011942 -0.0060001"/>
      <mass value="0.0042403"/>
      <inertia ixx="6.6232E-07" ixy="-1.5775E-08" ixz="1.8515E-12" iyy="2.1146E-07" iyz="-5.0828E-12" izz="6.9398E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link15_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link15_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_index_proximal_joint" type="revolute">
    <origin rpy="-3.1067 0 0" xyz="0.00028533 -0.13653 -0.032268"/>
    <parent link="right_hand_R_hand_base_link"/>
    <child link="right_hand_R_index_proximal"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
  </joint>
  <link name="right_hand_R_index_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0019697 0.019589 -0.005"/>
      <mass value="0.0045683"/>
      <inertia ixx="7.6111E-07" ixy="8.7637E-08" ixz="-3.7751E-13" iyy="9.6076E-08" iyz="9.9444E-13" izz="7.8179E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link16_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link16_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_index_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0026138 0.032026 -0.001"/>
    <parent link="right_hand_R_index_proximal"/>
    <child link="right_hand_R_index_intermediate"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
    <mimic joint="right_hand_R_index_proximal_joint" multiplier="1" offset="0"/>
  </joint>
  <link name="right_hand_R_index_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="right_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="right_hand_R_index_tip_joint" type="fixed">
    <parent link="right_hand_R_index_intermediate"/>
    <child link="right_hand_R_index_tip"/>
    <origin rpy="0 0 0" xyz="-0.005 0.04 -0.004"/>
  </joint>
  <!-- Middle Finger -->
  <link name="right_hand_R_middle_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.001297 0.011934 -0.0060001"/>
      <mass value="0.0042403"/>
      <inertia ixx="6.6211E-07" ixy="-1.8461E-08" ixz="1.8002E-12" iyy="2.1167E-07" iyz="-6.6808E-12" izz="6.9397E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link17_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link17_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_middle_proximal_joint" type="revolute">
    <origin rpy="-3.1416 0 0" xyz="0.00028533 -0.1371 -0.01295"/>
    <parent link="right_hand_R_hand_base_link"/>
    <child link="right_hand_R_middle_proximal"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
  </joint>
  <link name="right_hand_R_middle_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.001921 0.020796 -0.005"/>
      <mass value="0.0050396"/>
      <inertia ixx="9.5822E-07" ixy="1.1425E-07" ixz="-2.4791E-12" iyy="1.0646E-07" iyz="5.9173E-12" izz="9.8384E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link18_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link18_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_middle_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0024229 0.032041 -0.001"/>
    <parent link="right_hand_R_middle_proximal"/>
    <child link="right_hand_R_middle_intermediate"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
    <mimic joint="right_hand_R_middle_proximal_joint" multiplier="1" offset="0"/>
  </joint>
  <link name="right_hand_R_middle_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="right_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="right_hand_R_middle_tip_joint" type="fixed">
    <parent link="right_hand_R_middle_intermediate"/>
    <child link="right_hand_R_middle_tip"/>
    <origin rpy="0 0 0" xyz="-0.005 0.045 -0.004"/>
  </joint>
  <!-- Ring Finger -->
  <link name="right_hand_R_ring_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.001297 0.011934 -0.0060002"/>
      <mass value="0.0042403"/>
      <inertia ixx="6.6211E-07" ixy="-1.8461E-08" ixz="1.5793E-12" iyy="2.1167E-07" iyz="-6.6868E-12" izz="6.9397E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link19_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link19_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_ring_proximal_joint" type="revolute">
    <origin rpy="3.0892 0 0" xyz="0.00028533 -0.13691 0.0062872"/>
    <parent link="right_hand_R_hand_base_link"/>
    <child link="right_hand_R_ring_proximal"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
  </joint>
  <link name="right_hand_R_ring_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0021753 0.019567 -0.005"/>
      <mass value="0.0045683"/>
      <inertia ixx="7.6286E-07" ixy="8.0635E-08" ixz="-6.1562E-13" iyy="9.431E-08" iyz="5.8619E-13" izz="7.8177E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link20_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link20_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_ring_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0024229 0.032041 -0.001"/>
    <parent link="right_hand_R_ring_proximal"/>
    <child link="right_hand_R_ring_intermediate"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
    <mimic joint="right_hand_R_ring_proximal_joint" multiplier="1" offset="0"/>
  </joint>
  <link name="right_hand_R_ring_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="right_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="right_hand_R_ring_tip_joint" type="fixed">
    <parent link="right_hand_R_ring_intermediate"/>
    <child link="right_hand_R_ring_tip"/>
    <origin rpy="0 0 0" xyz="-0.002 0.04 -0.004"/>
  </joint>
  <!-- Pinky Finger -->
  <link name="right_hand_R_pinky_proximal">
    <inertial>
      <origin rpy="0 0 0" xyz="0.001297 0.011934 -0.0060001"/>
      <mass value="0.0042403"/>
      <inertia ixx="6.6211E-07" ixy="-1.8461E-08" ixz="1.6907E-12" iyy="2.1167E-07" iyz="-6.9334E-12" izz="6.9397E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link21_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link21_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_pinky_proximal_joint" type="revolute">
    <origin rpy="3.0369 0 0" xyz="0.00028533 -0.13571 0.025488"/>
    <parent link="right_hand_R_hand_base_link"/>
    <child link="right_hand_R_pinky_proximal"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
  </joint>
  <link name="right_hand_R_pinky_intermediate">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0024748 0.016203 -0.0050031"/>
      <mass value="0.0035996"/>
      <inertia ixx="4.3913E-07" ixy="4.1418E-08" ixz="3.7168E-11" iyy="7.0247E-08" iyz="5.8613E-11" izz="4.4867E-07"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link22_R.STL"/>
      </geometry>
      <material name="right_hand_mat_grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://dual_arm_model/meshes/inspire_hand/Link22_R.STL"/>
      </geometry>
    </collision>
  </link>
  <joint name="right_hand_R_pinky_intermediate_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0024229 0.032041 -0.001"/>
    <parent link="right_hand_R_pinky_proximal"/>
    <child link="right_hand_R_pinky_intermediate"/>
    <axis xyz="0 0 1"/>
    <limit effort="1" lower="0" upper="1.7" velocity="0.5"/>
    <mimic joint="right_hand_R_pinky_proximal_joint" multiplier="1" offset="0"/>
  </joint>
  <link name="right_hand_R_pinky_tip">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <sphere radius="0.005"/>
      </geometry>
      <material name="right_hand_mat_green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
  </link>
  <joint name="right_hand_R_pinky_tip_joint" type="fixed">
    <parent link="right_hand_R_pinky_intermediate"/>
    <child link="right_hand_R_pinky_tip"/>
    <origin rpy="0 0 0" xyz="-0.002 0.032 -0.004"/>
  </joint>
</robot>
