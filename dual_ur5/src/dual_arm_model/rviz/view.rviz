Panels:
  - Class: rviz_common/Displays
    Help Height: 78
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /Status1
        - /RobotModel1/Description Topic1
      Splitter Ratio: 0.5
    Tree Height: 549
  - Class: rviz_common/Selection
    Name: Selection
  - Class: rviz_common/Tool Properties
    Expanded:
      - /2D Goal Pose1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz_common/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz_common/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: ""
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz_default_plugins/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Alpha: 1
      Class: rviz_default_plugins/RobotModel
      Collision Enabled: false
      Description File: ""
      Description Source: Topic
      Description Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /robot_description
      Enabled: true
      Links:
        All Links Enabled: true
        Expand Joint Details: false
        Expand Link Details: false
        Expand Tree: false
        Link Tree Style: Links in Alphabetic Order
        left_base_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        left_ee_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        left_elbow_joint:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        left_shoulder_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        left_upper_arm_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        left_wrist_1_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        left_wrist_2_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        left_wrist_3_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        right_base_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        right_ee_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        right_elbow_joint:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        right_shoulder_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        right_upper_arm_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        right_wrist_1_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        right_wrist_2_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        right_wrist_3_link:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        stand:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
      Mass Properties:
        Inertia: false
        Mass: false
      Name: RobotModel
      TF Prefix: ""
      Update Interval: 0
      Value: true
      Visual Enabled: true
    - Class: rviz_default_plugins/TF
      Enabled: true
      Frame Timeout: 15
      Frames:
        All Enabled: true
        left_base_link:
          Value: true
        left_ee_link:
          Value: true
        left_elbow_joint:
          Value: true
        left_shoulder_link:
          Value: true
        left_upper_arm_link:
          Value: true
        left_wrist_1_link:
          Value: true
        left_wrist_2_link:
          Value: true
        left_wrist_3_link:
          Value: true
        right_base_link:
          Value: true
        right_ee_link:
          Value: true
        right_elbow_joint:
          Value: true
        right_shoulder_link:
          Value: true
        right_upper_arm_link:
          Value: true
        right_wrist_1_link:
          Value: true
        right_wrist_2_link:
          Value: true
        right_wrist_3_link:
          Value: true
        stand:
          Value: true
      Marker Scale: 1
      Name: TF
      Show Arrows: true
      Show Axes: true
      Show Names: false
      Tree:
        stand:
          left_base_link:
            left_shoulder_link:
              left_upper_arm_link:
                left_elbow_joint:
                  left_wrist_1_link:
                    left_wrist_2_link:
                      left_wrist_3_link:
                        left_ee_link:
                          {}
          right_base_link:
            right_shoulder_link:
              right_upper_arm_link:
                right_elbow_joint:
                  right_wrist_1_link:
                    right_wrist_2_link:
                      right_wrist_3_link:
                        right_ee_link:
                          {}
      Update Interval: 0
      Value: true
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Fixed Frame: stand
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz_default_plugins/Interact
      Hide Inactive Objects: true
    - Class: rviz_default_plugins/MoveCamera
    - Class: rviz_default_plugins/Select
    - Class: rviz_default_plugins/FocusCamera
    - Class: rviz_default_plugins/Measure
      Line color: 128; 128; 0
    - Class: rviz_default_plugins/SetInitialPose
      Covariance x: 0.25
      Covariance y: 0.25
      Covariance yaw: 0.06853891909122467
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /initialpose
    - Class: rviz_default_plugins/SetGoal
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /goal_pose
    - Class: rviz_default_plugins/PublishPoint
      Single click: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /clicked_point
  Transformation:
    Current:
      Class: rviz_default_plugins/TF
  Value: true
  Views:
    Current:
      Class: rviz_default_plugins/Orbit
      Distance: 4.035367488861084
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: 0.16394494473934174
        Y: -0.4031168222427368
        Z: 0.1691194772720337
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 0.785398006439209
      Target Frame: <Fixed Frame>
      Value: Orbit (rviz)
      Yaw: 0.785398006439209
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 846
  Hide Left Dock: false
  Hide Right Dock: false
  QMainWindow State: 000000ff00000000fd000000040000000000000156000002b0fc0200000008fb0000001200530065006c0065006300740069006f006e00000001e10000009b0000005c00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c006100790073010000003d000002b0000000c900fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261000000010000010f000002b0fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a00560069006500770073010000003d000002b0000000a400fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000004b00000003efc0100000002fb0000000800540069006d00650100000000000004b0000002fb00fffffffb0000000800540069006d006501000000000000045000000000000000000000023f000002b000000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: false
  Width: 1200
  X: 70
  Y: 399
