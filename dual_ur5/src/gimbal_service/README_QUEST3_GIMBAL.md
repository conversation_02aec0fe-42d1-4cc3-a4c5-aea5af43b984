# Quest3 云台控制系统启动说明

## 工作空间结构

本工作空间 (`test_ws/dual_ur5`) 已配置为支持Quest3 VR头显控制云台系统，主要组件包括：

1. **云台舵机服务** - 位于 `gimbal_service` 目录
2. **ROS2桥接程序** - 位于 `inspire_hand_ros2/TeleVision/teleop` 目录
3. **Quest3遥操作程序** - 位于 `inspire_hand_ros2/TeleVision/teleop` 目录

## 启动方法

### 方法1: 使用统一启动脚本（推荐）

```bash
# 在dual_ur5目录启动完整系统
#cd /home/<USER>/test_ws/dual_ur5
#./launch_quest3_gimbal.sh
```

此脚本会自动：
- 检查所有必要的环境和依赖
- 清理可能冲突的环境变量
- 编译gimbal_service包
- 按正确顺序启动三个组件
- 提供清晰的状态反馈

当你想要停止所有服务时，只需按Ctrl+C，脚本会自动清理所有后台进程。

### 方法2: 手动启动

如果需要手动启动各个组件，请按照以下步骤操作：

1. **编译gimbal_service**:
```bash
cd ~/test_ws/dual_ur5
colcon build --symlink-install
source install/setup.zsh

adb reverse tcp:8012 tcp:8012

sudo lsof -i :8012
#假设占用端口的PID为12345
kill -9 12345
adb reverse tcp:8012 tcp:8012
```

2. **启动云台舵机服务**（终端1）:
```bash
cd ~/test_ws/dual_ur5
source install/setup.zsh
ros2 run py_srvcli gimbal_service
```



3**启动Quest3遥操作程序**（终端2）:
```bash
cd ~/test_ws/dual_ur5
source install/setup.zsh
ros2 launch dual_ur_inspire_bringup vr_cam_bringup.launch.py
```

## 注意事项
- ls /dev/ttyUSB1   ls /dev/ttyUSB0
- 请确保使用dual_ur5目录下的启动脚本，而不是其他目录中的同名脚本
- 确保云台舵机已连接到 `/dev/ttyUSB0`
- 确保Quest3已连接并设置了端口转发: `adb reverse tcp:8012 tcp:8012`
- 如果遇到问题，请参考 `inspire_hand_ros2/TeleVision/Quest3_Gimbal_Control_README.md` 中的故障排除部分
