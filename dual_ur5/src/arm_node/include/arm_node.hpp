#include <rclcpp/rclcpp.hpp>
#include "trajectory_msgs/msg/joint_trajectory.hpp"
#include "trajectory_msgs/msg/joint_trajectory_point.hpp"

#include "geometry_msgs/msg/pose.hpp"
#include "geometry_msgs/msg/pose_stamped.hpp"
#include "std_msgs/msg/float64_multi_array.hpp"
#include "std_msgs/msg/bool.hpp"
#include "sensor_msgs/msg/joint_state.hpp"

#include <trac_ik/trac_ik.hpp>
#include <kdl/jntarray.hpp>
#include <kdl/frames.hpp>
#include <vector>
#include <thread>
#include <mutex>
#include <atomic>

class Arm: public rclcpp::Node
{
    public:
    explicit Arm(const rclcpp::NodeOptions & node_options);
    ~Arm();

    private:
    void arm_timer_callback();
    std::vector<double> manipulatorIK(geometry_msgs::msg::Pose target_pose);
    void send_joint_cmd(const std::vector<double>& pose);
    void init_params();
    void wrist_pose_Callback(const geometry_msgs::msg::PoseStamped::SharedPtr msg);
    bool is_pose_msg_changed(const geometry_msgs::msg::PoseStamped::SharedPtr& a,const geometry_msgs::msg::PoseStamped::SharedPtr& b,
        double position_tol = 1e-5,double orientation_tol = 1e-5);
    geometry_msgs::msg::Pose translate_pose(const geometry_msgs::msg::PoseStamped::SharedPtr& pose);
    void start_flag_Callback(const std_msgs::msg::Bool::SharedPtr msg);
    
    // Multi-threading IK functions
    void ik_computation_thread(geometry_msgs::msg::Pose target_pose);
    
    // 插值相关函数
    std::vector<double> interpolate(const std::vector<double>& start, const std::vector<double>& end, double t);

    geometry_msgs::msg::Pose get_incremental_goal(geometry_msgs::msg::Pose target_pose, std::vector<double>& start_point);

    // Multi-threading IK related
    std::mutex ik_mutex_;
    std::atomic<bool> ik_in_progress_{false};
    std::atomic<bool> new_ik_result_available_{false};
    std::vector<double> target_joint_positions_;
    std::thread ik_thread_;

    rclcpp::TimerBase::SharedPtr arm_timer_;
    rclcpp::Publisher<std_msgs::msg::Float64MultiArray>::SharedPtr controller_cmd_pub_;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr trans_pose_pub_;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr wrist_pose_sub_;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr start_flag_sub_;

    std::vector<double> last_left_pose_;
    std::atomic<bool> left_new_goal_received_ = false;
    geometry_msgs::msg::PoseStamped::SharedPtr left_pose_msg_;
    geometry_msgs::msg::PoseStamped::SharedPtr last_left_pose_msg_;

    std::mutex pose_mutex_;

    bool is_joint_init_ = false;

    // 插值相关变量
    std::vector<double> current_joint_positions_;
    std::vector<double> next_joint_positions_;
    int interpolation_steps_ = 3;
    int current_step_ = 0;
    bool interpolating_ = false;

    std::unique_ptr<TRAC_IK::TRAC_IK> ik_solver_;
    KDL::Chain kdl_chain_;
    KDL::JntArray joint_min_;
    KDL::JntArray joint_max_;
    KDL::JntArray joint_seed_;
    KDL::Frame desired_end_effector_pose_;

    const double change_threshold_ = 10.0;
    std_msgs::msg::Float64MultiArray desired_angle_;

    std::vector<double> origin_point_xyz_;
    std::vector<double> target_start_point_;
    bool record_flag_ = false;
    bool last_start_flag_ = false;

    std::string urdf_path_;
    std::string chain_root_;
    std::string chain_tip_;
    double robot_arm_length_;
    double human_arm_length_;
    double z_init_;
    double Z_bias_;
    double X_bias_;
    double Y_bias_;
    bool debugging_;
    bool is_measure_ik_times_;
    double incremental_ratio_;
};