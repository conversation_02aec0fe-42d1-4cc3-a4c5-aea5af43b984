#include "arm_node.hpp"

#include <urdf/model.h>
#include <kdl_parser/kdl_parser.hpp>
#include <fstream>

#include <cmath>

Arm::Arm(const rclcpp::NodeOptions &node_options) : Node("arm_node", node_options)
{
    init_params();

    arm_timer_ = this->create_wall_timer(std::chrono::duration<double>(1.0 / 100.0), 
                                    std::bind(&Arm::arm_timer_callback, this));
    controller_cmd_pub_ = this->create_publisher<std_msgs::msg::Float64MultiArray>(
        "/ur_arm_ros2_controller/commands", 10);
    trans_pose_pub_ = this->create_publisher<geometry_msgs::msg::PoseStamped>(
            "/trans_pose",rclcpp::SensorDataQoS());
    wrist_pose_sub_ = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            "/right/wrist_pose",
            rclcpp::SensorDataQoS(),
            std::bind(&Arm::wrist_pose_Callback, this, std::placeholders::_1)
        );
    start_flag_sub_ = this->create_subscription<std_msgs::msg::Bool>(
            "/start_flag",
            rclcpp::SensorDataQoS(),
            std::bind(&Arm::start_flag_Callback, this, std::placeholders::_1)
        );
    
    desired_angle_.data = {1.57,-1.30,1.706,-0.36,1.64,-0.785};

    target_joint_positions_.resize(6, 0.0);
    ik_in_progress_.store(false);
    new_ik_result_available_.store(false);
    
    target_start_point_.resize(3, 0.0);

    current_joint_positions_.resize(6, 0.0);
    next_joint_positions_.resize(6, 0.0);
    current_step_ = 0;
    interpolating_ = false;

    joint_seed_ = KDL::JntArray(6);
    joint_seed_.data << desired_angle_.data[0], desired_angle_.data[1], desired_angle_.data[2], 
                        desired_angle_.data[3], desired_angle_.data[4], desired_angle_.data[5];

    KDL::Tree tree;

    std::ifstream file(urdf_path_);
    if (!file)
    {
        RCLCPP_FATAL(this->get_logger(), "Failed to open URDF file at: %s", urdf_path_.c_str());
        return;
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string urdf_string = buffer.str();

    urdf::Model model;
    if (!model.initString(urdf_string))
    {
        RCLCPP_FATAL(this->get_logger(), "Failed to parse URDF string to URDF model.");
        return;
    }

    if (!kdl_parser::treeFromUrdfModel(model, tree))
    {
        RCLCPP_FATAL(this->get_logger(), "Failed to parse KDL tree from URDF model.");
        return;
    }

    RCLCPP_INFO(this->get_logger(), "Successfully loaded KDL tree from URDF.");

    if (!tree.getChain(chain_root_, chain_tip_, kdl_chain_)) {
        RCLCPP_FATAL(this->get_logger(), "Failed to extract KDL chain from URDF.");
        return;
    }

    unsigned int num_joints = kdl_chain_.getNrOfJoints();
    joint_min_.resize(num_joints);
    joint_max_.resize(num_joints);
    joint_seed_.resize(num_joints);

    unsigned int idx = 0;
    for (const auto& segment : kdl_chain_.segments) {
        const KDL::Joint& joint = segment.getJoint();
        if (joint.getType() == KDL::Joint::None) continue;

        auto urdf_joint = model.getJoint(joint.getName());
        if (urdf_joint && urdf_joint->limits) {
            joint_min_(idx) = urdf_joint->limits->lower;
            joint_max_(idx) = urdf_joint->limits->upper;
        } else {
            joint_min_(idx) = -M_PI;
            joint_max_(idx) = M_PI;
        }
        ++idx;
    }

    // if (joint_max_.rows() > 4) {
    //     double new_upper_limit = 180.0 * M_PI / 180.0;
    //     if (joint_max_(4) > new_upper_limit) {
    //         RCLCPP_INFO(this->get_logger(), "Overriding upper joint limit for left_wrist_2_joint. Old: %f, New: %f", joint_max_(4), new_upper_limit);
    //         joint_max_(4) = new_upper_limit;
    //     }
    //     double new_lower_limit = 0.0 * M_PI / 180.0;
    //     if (joint_min_(4) < new_lower_limit) {
    //         RCLCPP_INFO(this->get_logger(), "Overriding lower joint limit for left_wrist_2_joint. Old: %f, New: %f", joint_min_(4), new_lower_limit);
    //         joint_min_(4) = new_lower_limit;
    //     }
    // }

    if(debugging_)
    {
        idx = 0;
        for (const auto& segment : kdl_chain_.segments) {
            const KDL::Joint& joint = segment.getJoint();
            if (joint.getType() == KDL::Joint::None) continue;

            RCLCPP_INFO(this->get_logger(), "Joint %u (%s): min = %.6f, max = %.6f",
                        idx, joint.getName().c_str(), joint_min_(idx), joint_max_(idx));
            ++idx;
        }
    }

    ik_solver_ = std::make_unique<TRAC_IK::TRAC_IK>(kdl_chain_, joint_min_, joint_max_, 0.05, 1e-5, TRAC_IK::Distance);

}

void Arm::init_params()
{
    this->declare_parameter<std::string>("urdf_path", "");
    this->declare_parameter<std::string>("chain_root", "");
    this->declare_parameter<std::string>("chain_tip", "");
    this->declare_parameter<float>("robot_arm_length", 0.8);
    this->declare_parameter<float>("human_arm_length", 0.6);
    this->declare_parameter<float>("z_init", 1.0);
    this->declare_parameter<float>("Z_bias", 1.6);
    this->declare_parameter<float>("X_bias", 0.0);
    this->declare_parameter<float>("Y_bias", 0.0);
    this->declare_parameter<bool>("debugging", false);
    this->declare_parameter<bool>("is_measure_ik_times", false);
    this->declare_parameter<int>("interpolation_steps", 1);
    this->declare_parameter<std::vector<double>>("origin_point_xyz", {0.0, 0.0, 0.0});
    this->declare_parameter<double>("incremental_ratio", 1.0);

    urdf_path_ = this->get_parameter("urdf_path").as_string();
    chain_root_ = this->get_parameter("chain_root").as_string();
    chain_tip_ = this->get_parameter("chain_tip").as_string();
    robot_arm_length_ = this->get_parameter("robot_arm_length").as_double();
    human_arm_length_ = this->get_parameter("human_arm_length").as_double();
    z_init_ = this->get_parameter("z_init").as_double();
    Z_bias_ = this->get_parameter("Z_bias").as_double();
    X_bias_ = this->get_parameter("X_bias").as_double();
    Y_bias_ = this->get_parameter("Y_bias").as_double();    
    debugging_ = this->get_parameter("debugging").as_bool();
    is_measure_ik_times_ = this->get_parameter("is_measure_ik_times").as_bool();
    interpolation_steps_ = this->get_parameter("interpolation_steps").as_int();
    origin_point_xyz_ = this->get_parameter("origin_point_xyz").as_double_array();
    incremental_ratio_ = this->get_parameter("incremental_ratio").as_double();
}

Arm::~Arm()
{
    if (ik_thread_.joinable()) {
        ik_thread_.join();
    }
}

void Arm::wrist_pose_Callback(const geometry_msgs::msg::PoseStamped::SharedPtr msg)
{
    if (is_pose_msg_changed(msg, last_left_pose_msg_)) {
        left_pose_msg_ = msg;
        last_left_pose_msg_ = msg;
        left_new_goal_received_ = true;
    }
}

bool Arm::is_pose_msg_changed(const geometry_msgs::msg::PoseStamped::SharedPtr& a,const geometry_msgs::msg::PoseStamped::SharedPtr& b,
                                                                            double position_tol,double orientation_tol) 
{
    if (!a || !b) return true;
    if (std::abs(a->pose.position.x - b->pose.position.x) > position_tol ||
        std::abs(a->pose.position.y - b->pose.position.y) > position_tol ||
        std::abs(a->pose.position.z - b->pose.position.z) > position_tol) {
        return true;
    }

    if (std::abs(a->pose.orientation.x - b->pose.orientation.x) > orientation_tol ||
        std::abs(a->pose.orientation.y - b->pose.orientation.y) > orientation_tol ||
        std::abs(a->pose.orientation.z - b->pose.orientation.z) > orientation_tol ||
        std::abs(a->pose.orientation.w - b->pose.orientation.w) > orientation_tol) {
        return true;
    }

    return false;
}

geometry_msgs::msg::Pose Arm::translate_pose(const geometry_msgs::msg::PoseStamped::SharedPtr& pose)
{
    geometry_msgs::msg::Pose goal_pose;

    goal_pose.position.x = robot_arm_length_*(human_arm_length_+pose->pose.position.x)/human_arm_length_ + X_bias_;  // x
    goal_pose.position.y = pose->pose.position.y + Y_bias_;  // y 
    goal_pose.position.z = z_init_+pose->pose.position.z-Z_bias_;  // z

    goal_pose.orientation = pose->pose.orientation;

    return goal_pose;
}

void Arm::arm_timer_callback()
{
    geometry_msgs::msg::Pose left_goal_pose;
    geometry_msgs::msg::Pose incremental_goal_pose;
    bool triggered = false;

    if (left_new_goal_received_) {
        left_new_goal_received_ = false;
        left_goal_pose = translate_pose(left_pose_msg_);
        triggered = true;

        if(record_flag_)
        {
            target_start_point_[0] = left_goal_pose.position.x;
            target_start_point_[1] = left_goal_pose.position.y;
            target_start_point_[2] = left_goal_pose.position.z;
            record_flag_ = false;

            joint_seed_.data << 1.57,-1.30,1.706,-0.36,1.64,-0.785;

        }

        incremental_goal_pose = get_incremental_goal(left_goal_pose, target_start_point_);
        geometry_msgs::msg::PoseStamped incremental_goal_pose_stamped;
        incremental_goal_pose_stamped.pose = incremental_goal_pose;
        incremental_goal_pose_stamped.header.stamp = this->get_clock()->now();
        trans_pose_pub_->publish(incremental_goal_pose_stamped);
    }
    if (triggered && !ik_in_progress_.load() && last_start_flag_) {
        ik_in_progress_.store(true);
        
        if (ik_thread_.joinable()) {
            ik_thread_.detach();
        }
        
        ik_thread_ = std::thread(&Arm::ik_computation_thread, this, incremental_goal_pose);
    }

    if (interpolating_) {

        double t = static_cast<double>(current_step_) / static_cast<double>(interpolation_steps_);
        std::vector<double> interpolated_position = interpolate(current_joint_positions_, next_joint_positions_, t);
        
        send_joint_cmd(interpolated_position);
        
        current_step_++;
        
        if (current_step_ > interpolation_steps_) {
            interpolating_ = false;
            current_step_ = 0;
            current_joint_positions_ = next_joint_positions_;
        }
    }
    
    if (new_ik_result_available_.load()) {
        new_ik_result_available_.store(false);
        
        {
            std::lock_guard<std::mutex> lock(ik_mutex_);
            current_joint_positions_ = interpolating_ ? next_joint_positions_ : current_joint_positions_;
            next_joint_positions_ = target_joint_positions_;
        }
        
        interpolating_ = true;
        current_step_ = 0;
        
        if (current_joint_positions_[0] == 0.0 && 
            current_joint_positions_[1] == 0.0 && 
            current_joint_positions_[2] == 0.0 && 
            current_joint_positions_[3] == 0.0 && 
            current_joint_positions_[4] == 0.0 && 
            current_joint_positions_[5] == 0.0) {
            current_joint_positions_ = next_joint_positions_;
            interpolating_ = false;
            send_joint_cmd(current_joint_positions_);
        }
    }
}

void Arm::send_joint_cmd(const std::vector<double>& pose)
{    
    auto timestamp = this->get_clock()->now();
    std_msgs::msg::Float64MultiArray cmd_msg;
    cmd_msg.data = pose;

    trajectory_msgs::msg::JointTrajectoryPoint point;
    point.positions = pose;
    point.time_from_start = rclcpp::Duration::from_seconds(2.0);

    if(debugging_)
    {
        RCLCPP_INFO(this->get_logger(), "Command: [%f %f %f %f %f %f]",
        pose[0], pose[1], pose[2],
        pose[3], pose[4], pose[5]);
    }
    controller_cmd_pub_->publish(cmd_msg);
}

std::vector<double> Arm::manipulatorIK(geometry_msgs::msg::Pose target_pose)
{
    std::vector<double> ikJointValues;
    KDL::JntArray return_joints;
    double joint_angle_change;

    if(debugging_)
    {
            RCLCPP_INFO(this->get_logger(), "target_pose - Position: [%f, %f, %f], Orientation: [%f, %f, %f, %f]",
            target_pose.position.x,
            target_pose.position.y,
            target_pose.position.z,
            target_pose.orientation.x,
            target_pose.orientation.y,
            target_pose.orientation.z,
            target_pose.orientation.w
            );
    }

    double it[] = {target_pose.position.x, target_pose.position.y, target_pose.position.z};
    memcpy(desired_end_effector_pose_.p.data, it, sizeof(it));
    desired_end_effector_pose_.M = KDL::Rotation::Quaternion(
    target_pose.orientation.x,
    target_pose.orientation.y,
    target_pose.orientation.z,
    target_pose.orientation.w
    );
    joint_seed_.data << desired_angle_.data[0],desired_angle_.data[1],desired_angle_.data[2],desired_angle_.data[3],desired_angle_.data[4],desired_angle_.data[5];
    
    if(debugging_)
    {
        RCLCPP_INFO(this->get_logger(), "Seed: [%f %f %f %f %f %f]",
        joint_seed_(0), joint_seed_(1), joint_seed_(2),
        joint_seed_(3), joint_seed_(4), joint_seed_(5));

        for (int i = 0; i < 3; ++i) {
            RCLCPP_INFO(this->get_logger(), 
                "Rotation matrix row %d: [%.6f, %.6f, %.6f]",
                i,
                desired_end_effector_pose_.M(i, 0),
                desired_end_effector_pose_.M(i, 1),
                desired_end_effector_pose_.M(i, 2));
        }
    }

    int rc = ik_solver_->CartToJnt(joint_seed_, desired_end_effector_pose_, return_joints);
    
    if(rc>=0)
    {
        joint_angle_change = 0;
        for(int i=0;i<6;i++)
        {
            joint_angle_change += (return_joints(i) - desired_angle_.data[i])*(return_joints(i) - desired_angle_.data[i]);
        }
        
        if(joint_angle_change<change_threshold_) 
        {
            for(int i=0;i<6;i++) 
            {
                desired_angle_.data[i] = return_joints.data(i);
            }
                
        }
        else
        {

            RCLCPP_WARN(this->get_logger(),"Dramatic change occurred. Change: %f",joint_angle_change);

        }
    }
    else
    {
        RCLCPP_ERROR(this->get_logger(),"Did not find IK solution");
    }

    for(int i=0;i<6;i++)
        ikJointValues.push_back(desired_angle_.data[i]);

    return ikJointValues;
}

void Arm::ik_computation_thread(geometry_msgs::msg::Pose target_pose)
{
    auto start_time = std::chrono::high_resolution_clock::time_point();
    if(is_measure_ik_times_){
        start_time = std::chrono::high_resolution_clock::now();
    }
    
    std::vector<double> ikJointValues = manipulatorIK(target_pose);
    
    if(is_measure_ik_times_){
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        double duration_ms = duration.count() / 1000.0;
        RCLCPP_INFO(this->get_logger(), "Left manipulatorIK execution time: %.3f ms", duration_ms);
    }
    
    {
        std::lock_guard<std::mutex> lock(ik_mutex_);
        target_joint_positions_ = ikJointValues;
    }
    
    new_ik_result_available_.store(true);
    ik_in_progress_.store(false);
}

// 线性插值函数，t取值范围[0,1]
std::vector<double> Arm::interpolate(const std::vector<double>& start, const std::vector<double>& end, double t)
{
    if (start.size() != end.size()) {
        RCLCPP_ERROR(this->get_logger(), "插值起点和终点维度不匹配");
        return start;
    }
    
    std::vector<double> result(start.size());
    for (size_t i = 0; i < start.size(); ++i) {
        result[i] = start[i] + t * (end[i] - start[i]);
    }
    
    return result;
}

void Arm::start_flag_Callback(const std_msgs::msg::Bool::SharedPtr msg)
{
    if(msg->data && last_start_flag_ != msg->data)
    {
        record_flag_ = true;
    }
    last_start_flag_ = msg->data;
}

geometry_msgs::msg::Pose Arm::get_incremental_goal(geometry_msgs::msg::Pose target_pose, std::vector<double>& start_point)
{
    geometry_msgs::msg::Pose incremental_goal;
    std::vector<double> vr_to_arm_xyz_transform(3, 0.0); 

    vr_to_arm_xyz_transform[0] = start_point[0] - origin_point_xyz_[0];
    vr_to_arm_xyz_transform[1] = start_point[1] - origin_point_xyz_[1];
    vr_to_arm_xyz_transform[2] = start_point[2] - origin_point_xyz_[2];

    incremental_goal.position.x = (target_pose.position.x - vr_to_arm_xyz_transform[0]) * incremental_ratio_;
    incremental_goal.position.y = (target_pose.position.y - vr_to_arm_xyz_transform[1]) * incremental_ratio_;
    incremental_goal.position.z = (target_pose.position.z - vr_to_arm_xyz_transform[2]) * incremental_ratio_;
    incremental_goal.orientation = target_pose.orientation;
    
    return incremental_goal;
}

int main(int argc, char *argv[])
{
    rclcpp::init(argc, argv);

    auto node = std::make_shared<Arm>(rclcpp::NodeOptions());

    size_t thread_count = 4;
    rclcpp::executors::MultiThreadedExecutor executor(rclcpp::ExecutorOptions(), thread_count);
    executor.add_node(node);

    executor.spin();

    rclcpp::shutdown();
    return 0;
}